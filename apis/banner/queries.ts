import queryKeys from '@/utils/queryKeys';
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { getBannerRequest } from './requests';
import { IGetBannerResponse } from './types';

export const useGetBannerQuery = (options?: UseQueryOptions<IGetBannerResponse, AxiosError, IGetBannerResponse>) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.banner.item(),
    queryFn: () => getBannerRequest(),
    ...options,
  });
};
