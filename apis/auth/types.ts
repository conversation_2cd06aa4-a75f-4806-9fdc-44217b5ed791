import { TResponse } from '@/config/types';

export interface IRequestVerificationCodeBody {
  identifier: string;
}

export interface IVerifyCodeResponse {
  resetToken: string;
}

export interface IVerifyCodeBody {
  identifier: string;
  code: string;
}

export interface IUpdatePasswordBody {
  identifier: string;
  newPassword: string;
  resetToken: string;
}

export interface IUpdatePasswordResponse {
  user: IUserProfileResponse;
  tokens: ITokens;
}

export interface ILoginBody {
  subject?: string;
  password?: string;
  token?: string;
  type?: 'authentication' | 'access_token'; // Only pass into facebook provider
  provider: 'google' | 'facebook' | 'apple' | 'email' | 'spotify' | 'phone';
}

export interface ILoginResponseData {
  user: IUserProfileResponse;
  tokens: ITokens;
}

export interface ITokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface IRefreshTokenResponse extends TResponse<ITokens> {}

export interface ISpotifyRequestTokenPayload {
  code: string;
  codeVerifier: string;
  path: string;
}

export interface ISpotifyRequestTokenResponse {
  access_token: string;
  refresh_token: string;
}

export interface IUserProfileResponse {
  username: string;
  avatar: string;
  email: string;
  dateOfBirth: string;
  referral?: string;
  bio?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  id: number;
  isActive: boolean;
  type: 'free' | 'premium';
  status: 'update_profile' | 'plan_payment' | 'choose_interest' | 'choose_podcast' | 'complete';
  phoneNumber: string;
  hasPassword: boolean;
}

export interface IForgotPassBody {
  identifier: string;
}

export interface IForgotPassResponse {
  remainingTime: number;
}

export interface IAddPasswordBody {
  newPassword: string;
}

export interface IAddPasswordResponse {
  remainingTime: number;
}

export interface IChangePasswordBody {
  oldPassword: string;
  newPassword: string;
}

export interface IChangePasswordResponse {
  remainingTime: number;
}

export interface IVerifyPasswordBody {
  code: string;
}

export interface IVerifyPasswordResponse {
  resetToken: string;
}

export interface ICreateNewAccountBody {
  token: string;
  type?: 'authentication' | 'access_token';
  provider: 'google' | 'facebook' | 'apple' | 'email' | 'spotify' | 'phone';
}

export interface ICreateNewAccountResponse {
  user: IUserProfileResponse;
  tokens: ITokens;
}
