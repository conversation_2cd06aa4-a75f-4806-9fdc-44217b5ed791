import { APP_SCHEME, env } from '@/utils/const';
import axios from 'axios';
import { makeRedirectUri } from 'expo-auth-session';
import { request } from '../axios';
import {
  IForgotPassBody,
  IForgotPassResponse,
  ILoginBody,
  ILoginResponseData,
  IRefreshTokenResponse,
  IRequestVerificationCodeBody,
  ISpotifyRequestTokenPayload,
  ISpotifyRequestTokenResponse,
  IUpdatePasswordBody,
  IUserProfileResponse,
  IVerifyCodeBody,
  IAddPasswordBody,
  IAddPasswordResponse,
  IChangePasswordBody,
  IChangePasswordResponse,
  IVerifyPasswordBody,
  IVerifyPasswordResponse,
  ICreateNewAccountBody,
  ICreateNewAccountResponse,
} from './types';
import { useCommonStore } from '@/store/common';

export const requestVerificationRequest = async (body: IRequestVerificationCodeBody) => {
  const { data } = await request({
    url: `${env.API_VERSION}/auth/request-verification`,
    method: 'POST',
    data: body,
  });

  return data;
};

export const requestUpdateInfoVerificationRequest = async (body: IRequestVerificationCodeBody) => {
  const { data } = await request({
    url: `${env.API_VERSION}/auth/request-update-info-verification`,
    method: 'POST',
    data: body,
  });

  return data;
};

export const refreshTokenRequest = async (refreshToken: string): Promise<IRefreshTokenResponse> => {
  const { isStagingENV } = useCommonStore.getState();

  const { data } = await axios({
    url:
      (isStagingENV ? env.API_URL_STAGING : env.API_URL_DEV) + env.API_PREFIX + `${env.API_VERSION}/auth/refresh-token`,
    method: 'POST',
    headers: {
      Authorization: `Bearer ${refreshToken}`,
    },
  });

  return data;
};

export const loginRequest = async (body: ILoginBody): Promise<ILoginResponseData> => {
  const { data } = await request({
    method: 'POST',
    url: `${env.API_VERSION}/auth/login`,
    data: body,
  });

  return data?.data;
};

export const spotifyGetTokenRequest = async (
  payload: ISpotifyRequestTokenPayload
): Promise<ISpotifyRequestTokenResponse> => {
  const body = {
    code: payload.code,
    redirect_uri: makeRedirectUri({
      scheme: APP_SCHEME,
      path: payload.path || 'sign-in',
    }),
    grant_type: 'authorization_code',
    client_id: env.SPOTIFY_CLIENT_ID,
    code_verifier: payload.codeVerifier,
  };

  const formBody = new URLSearchParams(body).toString();

  const { data } = await axios({
    method: 'POST',
    url: 'https://accounts.spotify.com/api/token',
    data: formBody,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  return data;
};

export const verifyCodeRequest = async (body: IVerifyCodeBody) => {
  const { data } = await request({
    url: `${env.API_VERSION}/auth/verify-code`,
    method: 'POST',
    data: body,
  });

  return data.data;
};

export const verifyCodeForUpdateProfileRequest = async (body: IVerifyCodeBody) => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/verify-code`,
    method: 'POST',
    data: body,
  });

  return data.data;
};

export const updatePasswordRequest = async ({ resetToken, ...rest }: IUpdatePasswordBody) => {
  const { isStagingENV } = useCommonStore.getState();

  const { data } = await axios({
    baseURL: (isStagingENV ? env.API_URL_STAGING : env.API_URL_DEV) + env.API_PREFIX,
    url: `${env.API_VERSION}/auth/update-password`,
    method: 'POST',
    data: rest,
    headers: {
      Authorization: `Bearer ${resetToken}`,
    },
  });

  return data.data;
};

export const getUserProfileRequest = async (): Promise<IUserProfileResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/profile`,
    method: 'GET',
  });

  return {
    ...data.data.user,
    hasPassword: data.data.hasPassword,
  };
};

export const forgotPassRequest = async (body: IForgotPassBody): Promise<IForgotPassResponse> => {
  const { data } = await request({
    method: 'POST',
    url: `${env.API_VERSION}/auth/forgot-password`,
    data: body,
  });

  return data?.data;
};

export const addPasswordRequest = async (body: IAddPasswordBody): Promise<IAddPasswordResponse> => {
  const { data } = await request({
    method: 'POST',
    url: `${env.API_VERSION}/user/add-password`,
    data: body,
  });

  return data?.data;
};

export const changePasswordRequest = async (body: IChangePasswordBody): Promise<IChangePasswordResponse> => {
  const { data } = await request({
    method: 'PUT',
    url: `${env.API_VERSION}/user/update-password`,
    data: body,
  });

  return data?.data;
};

export const verifyPasswordRequest = async (body: IVerifyPasswordBody): Promise<IVerifyPasswordResponse> => {
  const { data } = await request({
    method: 'POST',
    url: `${env.API_VERSION}/user/verify-password`,
    data: body,
  });

  return data?.data;
};

export const createNewAccountRequest = async (body: ICreateNewAccountBody): Promise<ICreateNewAccountResponse> => {
  const { data } = await request({
    method: 'POST',
    url: `${env.API_VERSION}/auth/create-user`,
    data: body,
  });

  return data?.data;
};
