import { env } from '@/utils/const';
import { request } from '../axios';
import { ISource } from '../params';
import {
  ICommentEpisodeResponse,
  ICommentReply,
  ICommentReplyListResponse,
  ICommentResponse,
  ICreateCommentEpisodeParams,
  ICreateCommentParams,
  IDeleteCommentParams,
  IEditReplyPostPayload,
  IGetCommentReplyListParams,
  IGetCommentsParams,
  IGetCommentsResponse,
  IGetEpisodeCommentsParams,
  IGetEpisodeCommentsResponse,
  ILikeCommentResponse,
  IReplyPostPayload,
  IUpdateCommentParams,
  IUpdateEpisodeCommentParams,
  IUploadCommentImageParams,
  IUploadCommentImageResponse,
} from './types';

export const getCommentsRequest = async (params: IGetCommentsParams): Promise<IGetCommentsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast/community/${params.podcastId}`,
    method: 'GET',
    params,
  });

  return data;
};

export const getCommentByIdRequest = async (id: string, source: 'local' | 'podchaser'): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/${id}/${source}`,
    method: 'GET',
  });

  return data.data;
};

export const getEpisodeCommentByIdRequest = async (
  id: string,
  source: 'local' | 'podchaser'
): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/${id}/${source}`,
    method: 'GET',
  });

  return data.data;
};

export const getCommentReplyListRequest = async (
  params: IGetCommentReplyListParams
): Promise<ICommentReplyListResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply`,
    method: 'GET',
    params,
  });

  return data;
};

export const getCommentEpisodeReplyListRequest = async (
  params: IGetCommentReplyListParams
): Promise<ICommentReplyListResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply`,
    method: 'GET',
    params,
  });

  return data;
};

export const createCommentRequest = async (params: ICreateCommentParams): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const createCommentEpisodeRequest = async (
  params: ICreateCommentEpisodeParams
): Promise<ICommentEpisodeResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const uploadCommentImageRequest = async (
  params: IUploadCommentImageParams
): Promise<IUploadCommentImageResponse[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/upload`,
    method: 'POST',
    data: params,
  });

  return data.data;
};

export const deleteCommentRequest = async (params: IDeleteCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment/${params.id}`,
    method: 'DELETE',
  });
};

export const updateCommentRequest = async ({ id, ...rest }: IUpdateCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment/${id}`,
    method: 'PATCH',
    data: rest,
  });
};

export const replyCommentRequest = async (params: IReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply`,
    method: 'POST',
    data: params,
  });

  return data?.data;
};

export const editReplyCommentRequest = async ({ id, ...params }: IEditReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply/${id}`,
    method: 'PATCH',
    data: params,
  });

  return data?.data;
};

export const editReplyCommentEpisodeRequest = async ({
  id,
  ...params
}: IEditReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply/${id}`,
    method: 'PATCH',
    data: params,
  });

  return data?.data;
};

export const replyCommentEpisodeRequest = async (params: IReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply`,
    method: 'POST',
    data: params,
  });

  return data?.data;
};

export const deleteReplyCommentRequest = async ({ id, source }: { id: string; source: ISource }): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment-reply/${id}/${source}`,
    method: 'DELETE',
  });
};

export const deleteReplyCommentEpisodeRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment-reply/${id}/${source}`,
    method: 'DELETE',
  });
};

export const likeCommentRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<ILikeCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/${id}/like/${source}`,
    method: 'POST',
  });

  return data?.data;
};

export const getEpisodeCommentsRequest = async (
  params: IGetEpisodeCommentsParams
): Promise<IGetEpisodeCommentsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment`,
    method: 'GET',
    params,
  });

  return data;
};

export const deleteEpisodeCommentRequest = async (params: IDeleteCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment/${params.id}`,
    method: 'DELETE',
  });
};

export const likeEpisodeCommentRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<ILikeCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/${id}/like/${source}`,
    method: 'POST',
  });

  return data?.data;
};

export const updateEpisodeCommentRequest = async ({ id, ...rest }: IUpdateEpisodeCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment/${id}`,
    method: 'PATCH',
    data: rest,
  });
};
