import { InfiniteData, UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ISource } from '../params';
import {
  createCommentEpisodeRequest,
  createCommentRequest,
  deleteCommentRequest,
  deleteEpisodeCommentRequest,
  deleteReplyCommentEpisodeRequest,
  deleteReplyCommentRequest,
  editReplyCommentEpisodeRequest,
  editReplyCommentRequest,
  likeCommentRequest,
  likeEpisodeCommentRequest,
  replyCommentEpisodeRequest,
  replyCommentRequest,
  updateCommentRequest,
  updateEpisodeCommentRequest,
  uploadCommentImageRequest,
} from './requests';
import {
  ICommentEpisodeResponse,
  ICommentReply,
  ICommentResponse,
  ICreateCommentEpisodeParams,
  ICreateCommentParams,
  IDeleteCommentParams,
  IEditReplyPostPayload,
  IGetCommentsResponse,
  ILikeCommentResponse,
  IReplyPostPayload,
  IUpdateCommentParams,
  IUpdateEpisodeCommentParams,
  IUploadCommentImageParams,
  IUploadCommentImageResponse,
} from './types';
import queryKeys from '@/utils/queryKeys';
import { handleToggleLikePost } from '../settled-handler/like';
import {
  handleCreateComment,
  handleCreateReplyComment,
  handleDeleteComment,
  handleDeleteReplyComment,
  handleUpdateComment,
  handleUpdateReplyComment,
} from '../settled-handler/comment';

// Create comment mutation
export const useCreateCommentMutation = (
  options?: UseMutationOptions<ICommentResponse, AxiosError, ICreateCommentParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateCommentParams) => createCommentRequest(params),
    onSettled(data, error, variables) {
      handleCreateComment(queryClient, error);
    },
    ...options,
  });
};

export const useCreateCommentEpisodeMutation = (
  options?: UseMutationOptions<ICommentEpisodeResponse, AxiosError, ICreateCommentEpisodeParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateCommentEpisodeParams) => createCommentEpisodeRequest(params),
    onSettled(data, error, variables) {
      handleCreateComment(queryClient, error);
    },
    ...options,
  });
};

// Upload comment image mutation
export const useUploadCommentImageMutation = (
  options?: UseMutationOptions<IUploadCommentImageResponse[], AxiosError, IUploadCommentImageParams>
) => {
  return useMutation({
    mutationFn: (params: IUploadCommentImageParams) => uploadCommentImageRequest(params),
    ...options,
  });
};

// Delete comment mutation
export const useDeleteCommentMutation = (options?: UseMutationOptions<void, AxiosError, IDeleteCommentParams>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteCommentParams) => deleteCommentRequest(params),
    onSettled(data, error) {
      handleDeleteComment(queryClient, error);
    },
    ...options,
  });
};
export const useUpdateCommentMutation = (options?: UseMutationOptions<void, AxiosError, IUpdateCommentParams>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCommentRequest,
    onSettled(data, error) {
      handleUpdateComment(queryClient, error);
    },
    ...options,
  });
};

export const useReplyCommentMutation = (options?: UseMutationOptions<ICommentReply, AxiosError, IReplyPostPayload>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IReplyPostPayload) => replyCommentRequest(params),
    onSettled: (data, error, variables) => {
      handleCreateReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useEditReplyCommentMutation = (
  options?: UseMutationOptions<ICommentReply, AxiosError, IEditReplyPostPayload>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IEditReplyPostPayload) => editReplyCommentRequest(params),
    onSettled: (data, error) => {
      handleUpdateReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useEditReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<ICommentReply, AxiosError, IEditReplyPostPayload>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IEditReplyPostPayload) => editReplyCommentEpisodeRequest(params),
    onSettled: (data, error) => {
      handleUpdateReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<ICommentReply, AxiosError, IReplyPostPayload>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IReplyPostPayload) => replyCommentEpisodeRequest(params),
    onSettled: (data, error, variables) => {
      handleCreateReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useDeleteReplyCommentMutation = (
  options?: UseMutationOptions<void, AxiosError, { id: string; source: ISource }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteReplyCommentRequest,
    onSettled(data, error) {
      handleDeleteReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useDeleteReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<void, AxiosError, { id: string; source: ISource }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteReplyCommentEpisodeRequest,
    onSettled(data, error) {
      handleDeleteReplyComment(queryClient, error);
    },
    ...options,
  });
};

export const useLikeCommentMutation = (
  options?: UseMutationOptions<ILikeCommentResponse, AxiosError, { id: string; source: ISource }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => likeCommentRequest(payload),
    onMutate: async ({ id, source }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

      queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
        { queryKey: queryKeys.comments.list() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => ({
              ...page,
              data: page.data.map((comment) =>
                comment.id === id
                  ? {
                      ...comment,
                      hasLiked: !comment.hasLiked,
                      likeCount: comment.hasLiked
                        ? (Number(comment.likeCount) - 1).toString()
                        : (Number(comment.likeCount) + 1).toString(),
                    }
                  : comment
              ),
            })),
          };
        }
      );

      queryClient.setQueriesData<ICommentResponse>(
        {
          queryKey: queryKeys.comments.item(),
        },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            hasLiked: !oldData.hasLiked,
            likeCount: oldData.hasLiked
              ? (Number(oldData.likeCount) - 1).toString()
              : (Number(oldData.likeCount) + 1).toString(),
          };
        }
      );
    },
    onSettled(data, error) {
      handleToggleLikePost(queryClient, error);
    },
    ...options,
  });
};

// Episode comment mutations
export const useDeleteEpisodeCommentMutation = (
  options?: UseMutationOptions<void, AxiosError, IDeleteCommentParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteCommentParams) => deleteEpisodeCommentRequest(params),
    onSettled(data, error) {
      handleDeleteComment(queryClient, error);
    },
    ...options,
  });
};

export const useLikeEpisodeCommentMutation = (
  options?: UseMutationOptions<ILikeCommentResponse, AxiosError, { id: string; source: ISource }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => likeEpisodeCommentRequest(payload),
    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode(variables.id) });

      const snapshot = queryClient.getQueryData<ICommentResponse>(
        queryKeys.comments.itemEpisode(variables.id, variables.source)
      );
      if (!snapshot) return;
      const newSnapshot = {
        ...snapshot,
        hasLiked: !snapshot?.hasLiked,
        likeCount: snapshot?.hasLiked
          ? (Number(snapshot?.likeCount) - 1).toString()
          : (Number(snapshot?.likeCount) + 1).toString(),
      };

      queryClient.setQueryData(queryKeys.comments.itemEpisode(variables.id, variables.source), newSnapshot);
    },
    onSettled(data, error) {
      handleToggleLikePost(queryClient, error);
    },
    ...options,
  });
};

export const useUpdateEpisodeCommentMutation = (
  options?: UseMutationOptions<void, AxiosError, IUpdateEpisodeCommentParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateEpisodeCommentRequest,
    onSettled(data, error) {
      handleUpdateComment(queryClient, error);
    },
    ...options,
  });
};
