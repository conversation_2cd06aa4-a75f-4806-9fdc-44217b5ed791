import { UseQueryOptions, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import {
  getCommentByIdRequest,
  getCommentEpisodeReplyListRequest,
  getCommentReplyListRequest,
  getCommentsRequest,
  getEpisodeCommentByIdRequest,
  getEpisodeCommentsRequest,
} from './requests';
import {
  ICommentResponse,
  IGetCommentReplyListParams,
  IGetCommentsParams,
  IGetCommentsResponse,
  IGetEpisodeCommentsParams,
} from './types';

import queryKeys from '@/utils/queryKeys';
import { AxiosError } from 'axios';
import { ISource } from '../params';

export const useGetCommentsQuery = (
  params: IGetCommentsParams,
  options?: UseQueryOptions<IGetCommentsResponse, AxiosError, IGetCommentsResponse>
) => {
  return useQuery({
    queryKey: queryKeys.comments.list(params),
    queryFn: () => getCommentsRequest(params),
    ...options,
  });
};

export const useGetCommentsInfiniteQuery = (params: IGetCommentsParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.podcastId,
    queryKey: queryKeys.comments.list(params),
    refetchOnMount: true,
    queryFn: ({ pageParam = 1 }) => getCommentsRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    // maxPages: 2,
  });
};

export const useGetCommentByIdQuery = (
  id: string,
  source: ISource,
  options?: Partial<UseQueryOptions<ICommentResponse, AxiosError, ICommentResponse>>
) => {
  return useQuery({
    queryKey: queryKeys.comments.item(id, source),
    queryFn: () => getCommentByIdRequest(id, source),
    enabled: !!id,
    refetchOnMount: true,
    ...options,
  });
};

export const useGetEpisodeCommentByIdQuery = (
  id: string,
  source: ISource,
  options?: Partial<UseQueryOptions<ICommentResponse, AxiosError, ICommentResponse>>
) => {
  return useQuery({
    queryKey: queryKeys.comments.itemEpisode(id, source),
    queryFn: () => getEpisodeCommentByIdRequest(id, source),
    enabled: !!id,
    refetchOnMount: true,
    ...options,
  });
};

export const useGetInfiniteCommentReplyListQuery = (params: IGetCommentReplyListParams, { enabled = true } = {}) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    queryKey: queryKeys.commentsReply.list(params),
    queryFn: ({ pageParam = 1 }) => getCommentReplyListRequest({ ...params, page: pageParam }),
    enabled: !!params.commentId && enabled,
    refetchOnMount: true,
    getNextPageParam: (lastPage, allPages = []) => {
      return lastPage.meta.totalPages > allPages.length ? allPages.length + 1 : undefined;
    },
  });
};

export const useGetInfiniteCommentEpisodeReplyListQuery = (params: IGetCommentReplyListParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    queryKey: queryKeys.commentsReply.listEpisode(params),
    queryFn: ({ pageParam = 1 }) => getCommentEpisodeReplyListRequest({ ...params, page: pageParam }),
    enabled: !!params.commentId,
    getNextPageParam: (lastPage, allPages = []) => {
      return lastPage.meta.totalPages > allPages.length ? allPages.length + 1 : undefined;
    },
  });
};

export const useGetEpisodeCommentsInfiniteQuery = (params: IGetEpisodeCommentsParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.episodeId,
    queryKey: queryKeys.comments.listEpisode(params),
    refetchOnMount: true,
    queryFn: ({ pageParam = 1 }) => getEpisodeCommentsRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage, allPages = []) => {
      return lastPage.meta.totalPages > allPages.length ? allPages.length + 1 : undefined;
    },
  });
};
