import queryKeys from '@/utils/queryKeys';
import { InfiniteData, QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { IPostHistoryResponse } from '../user';

export type CustomVariables = {
  podcastId?: string;
  episodeId?: string;
};

export const handleToggleLikePodcast = (
  queryClient: QueryClient,
  error?: AxiosError<unknown, any> | null,
  variables?: CustomVariables
) => {
  if (error) {
    queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.item() });
  }

  queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserLikesPodcastsHistoryInfinite() });

  if (variables?.podcastId) {
    queryClient.setQueriesData<InfiniteData<IPostHistoryResponse>>(
      { queryKey: queryKeys.userProfile.postUserHistoryInfinite() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((item) => {
              return item.podcastId === variables?.podcastId ? { ...item, hasLiked: !item.hasLiked } : item;
            }),
          })),
        };
      }
    );
  } else {
    queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  }
};

export const handleToggleLikeEpisode = (
  queryClient: QueryClient,
  error?: AxiosError<unknown, any> | null,
  variables?: CustomVariables
) => {
  if (error) {
    queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
    queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodeByIdRequest() });
  }

  queryClient.invalidateQueries({ queryKey: queryKeys.favorites.episodesInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserLikesEpisodesHistoryInfinite() });

  if (variables?.episodeId) {
    queryClient.setQueriesData<InfiniteData<IPostHistoryResponse>>(
      { queryKey: queryKeys.userProfile.postUserHistoryInfinite() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((item) => {
              return item.parentId === variables?.episodeId ? { ...item, hasLiked: !item.hasLiked } : item;
            }),
          })),
        };
      }
    );
  } else {
    queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  }
};

export const handleToggleLikePost = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserLikesPostsHistoryInfinite() });

  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
};
