import queryKeys from '@/utils/queryKeys';
import { QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export const handleDeleteComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
};

export const handleCreateComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.podcastsInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
};

export const handleUpdateComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
};

export const handleCreateReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });
};

export const handleUpdateReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });

  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
};

export const handleDeleteReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
};
