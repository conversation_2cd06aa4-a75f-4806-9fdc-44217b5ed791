import { ThemedText } from '@/components/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ToastType } from 'toastify-react-native/utils/interfaces';
import { useUserStore } from '@/store/user';

const errorStyles = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.stateError,
    borderRadius: 999,
    gap: 16,
    maxWidth: '80%',
    width: 'auto',
  },
  imageStyle: {},
  description: {
    fontSize: 14,
    color: theme.colors.neutralWhite,
    ...theme.fw500,
    flexWrap: 'nowrap',
    width: 'auto',
    maxWidth: '85%',
  },
}));

const successStyles = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.primary,
    borderRadius: 999,
    gap: 16,
    maxWidth: '80%',
    width: 'auto',
  },
  imageStyle: {},
  description: {
    fontSize: 14,
    color: theme.colors.background,
    ...theme.fw500,
    flexWrap: 'nowrap',
    width: 'auto',
    maxWidth: '85%',
  },
}));

type IToast = {
  title?: string;
  description: string;
};

const CustomErrorToasterComponent = ({ description }: IToast) => {
  const { styles } = useStyles(errorStyles);
  return (
    <SafeAreaView>
      <TouchableOpacity activeOpacity={1} onPressOut={() => Toast.hide()} style={styles.container}>
        <Image source={require('@/assets/images/alert-icon.png')} style={styles.imageStyle} />

        <ThemedText style={styles.description}>{description}</ThemedText>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const CustomSuccessToasterComponent = ({ description }: IToast) => {
  const { styles, theme } = useStyles(successStyles);

  return (
    <SafeAreaView>
      <TouchableOpacity activeOpacity={1} onPressOut={() => Toast.hide()} style={styles.container}>
        <Ionicons color={theme.colors.background} name='checkmark-circle' size={24} />

        <ThemedText style={styles.description}>{description}</ThemedText>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export const toastError = (error: any) => {
  console.error(error);

  const statusCode = error?.code;
  const { setIsShowWarningRestricted } = useUserStore.getState();

  const errorMessage =
    typeof error === 'string' ? error : (error?.message ?? (typeof error === 'string' ? error : 'An error occurred'));

  if (typeof error !== 'string' && statusCode === 403) {
    setIsShowWarningRestricted(true);
    return;
  }

  Toast.hide();
  Toast.show({
    type: 'errorToast' as ToastType,
    props: { description: errorMessage },
    topOffset: 16,
    position: 'top',
    autoHide: true,
    useModal: false,
  });
};

export const toastSuccess = ({ title, description }: IToast) => {
  Toast.hide();
  Toast.show({
    type: 'successToast' as ToastType,
    props: { title, description },
    bottomOffset: 54,
    autoHide: true,
    position: 'bottom',
    useModal: false,
  });
};

export const toastConfig = {
  successToast: ({ props }: any) => {
    const { description, title } = props;
    return <CustomSuccessToasterComponent description={description} title={title} />;
  },
  errorToast: ({ props }: any) => {
    const { description, title } = props;
    return <CustomErrorToasterComponent description={description} title={title} />;
  },
};
