import { useGetSubscriptionStatusQuery } from '@/apis/user';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { CustomButton } from '@/components/ui/CustomButton';
import { useSubscriptionContext } from '@/contexts/subscription.context';
import { PATRON_PRODUCT_ID } from '@/modules/choose-plan/ChoosePlanProvider';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { Skeleton } from 'moti/skeleton';
import { useMemo, useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const CurrentPlan = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const { cancelPurchase, isPurchasing, customerInfo } = useSubscriptionContext();
  const { data: subscriptionStatus } = useGetSubscriptionStatusQuery();

  const premium = customerInfo?.subscriptionsByProductIdentifier[PATRON_PRODUCT_ID];
  const premiumExpired = premium?.expiresDate || subscriptionStatus?.subscription?.expiresAt;
  const premiumCancelled = subscriptionStatus?.subscription?.status === 'cancelled' || !premium?.willRenew;
  const premiumActive =
    premium?.isActive ||
    subscriptionStatus?.subscription?.status === 'active' ||
    (!premiumCancelled && !!premiumExpired && new Date(premiumExpired) > new Date());

  const handleShowModal = (isShow: boolean) => {
    setIsModalVisible(isShow);
  };

  const handleAction = () => {
    if (!subscriptionStatus) return;
    if (!premiumActive || premiumCancelled) {
      return router.push({
        pathname: '/choose-plan',
        params: { isAccountUpdate: 'true' },
      });
    }

    // confirm cancel subscription
    handleShowModal(true);
  };

  const handleCancel = () => {
    handleShowModal(false);
  };

  const handleCancelSubscription = async () => {
    await cancelPurchase();
    handleShowModal(false);
  };

  const currentPlan = premiumActive ? 'Founding Patron' : 'Founding Member';

  const actionTitle = useMemo(() => {
    if (premiumCancelled && premiumExpired && new Date(premiumExpired) > new Date()) return 'Resubscribe';
    if (premiumActive) return 'Cancel Subscription';
    return 'Upgrade Subscription';
  }, [premiumActive, premiumCancelled, premiumExpired]);

  const expiredTime = useMemo(() => {
    if (!premiumExpired) return '';

    if (premiumCancelled)
      return `You'll keep your benefits until ${format(new Date(premiumExpired), 'MMM dd, yyyy')} , & won't be charged again`;

    if (premiumActive) return `Renew on ${format(new Date(premiumExpired), 'MMM dd, yyyy')}`;

    return '';
  }, [premiumExpired, premiumActive, premiumCancelled]);

  if (!subscriptionStatus)
    return (
      <View style={styles.container}>
        <Skeleton width={150} height={28} />

        <Skeleton width={153} height={38} radius={999} />
      </View>
    );

  return (
    <View style={styles.container}>
      <View style={styles.planInfo}>
        <ThemedText type='subtitleSemiBold'>{currentPlan}</ThemedText>

        <Show when={premiumActive}>
          <Spacer height={8} />

          <ThemedText type='smallNormal'>{expiredTime}</ThemedText>
        </Show>
      </View>

      <View>
        <CustomButton style={styles.actionButton} textType='tinySemiBold' onPress={handleAction}>
          {actionTitle}
        </CustomButton>
      </View>

      <ConfirmationModal
        isVisible={isModalVisible}
        description=''
        title='Are you sure you want to cancel subscription?'
        onCancel={handleCancel}
        onClose={() => {
          handleShowModal(false);
        }}
        onConfirm={handleCancelSubscription}
        cancelText='Nevermind'
        confirmText='Cancel Subscription'
        isLoading={isPurchasing}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    borderColor: theme.colors.neutralBackground,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  planInfo: {
    flex: 1,
  },
  actionButton: {
    minHeight: 38,
  },
}));
