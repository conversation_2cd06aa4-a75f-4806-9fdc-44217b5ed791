import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Image, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Benefit } from './Benefit';
import { Icons } from '@/assets/icons';

export const BenefitCard = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <ThemedText type='subtitleMedium'>Upgrade Founding Patron Benefit</ThemedText>

      <Spacer height={24} />

      <ThemedText type='smallNormal'>
        Support the team and help us build even more amazing features! By subscribing, you directly contribute to future
        updates, and get benefit:
      </ThemedText>

      <Spacer height={20} />

      <Benefit icon={<Icons.RabidCoin size={24} color='#D9FF03' />} title="Commemorative Rabid's Metal Coin" />

      <Spacer height={12} />

      <Benefit
        icon={<Image source={require('@/assets/images/profile-icon.png')} style={{ width: 24, height: 24 }} />}
        title='Patron Special Profile'
      />

      <Spacer height={12} />

      <Benefit icon={<Icons.EditV2 size={24} color='#D9FF03' />} title='Edit Comment/Reply' />

      <Spacer height={12} />

      <Benefit icon={<Icons.Verify size={24} color='#D9FF03' />} title='Verified Profile' />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    padding: 24,
  },
}));
