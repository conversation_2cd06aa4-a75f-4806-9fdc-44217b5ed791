import { HeaderStyle } from '@/components/HeaderStyle';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { CurrentPlan } from './components/CurrentPlan';
import { BenefitCard } from './components/BenefitCard';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import Animated from 'react-native-reanimated';

type Props = {};

export const SubscriptionPlan = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  return (
    <View style={styles.container}>
      <HeaderStyle title='Subscription Plan' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <Animated.ScrollView style={styles.box} onScroll={onScroll} contentContainerStyle={{ paddingTop: headerHeight }}>
        <ThemedText type='defaultMedium'>Current Plan</ThemedText>

        <Spacer height={20} />

        <CurrentPlan />

        <Spacer height={40} />

        <BenefitCard />
      </Animated.ScrollView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  box: {
    paddingVertical: 40,
    paddingHorizontal: 24,
  },
}));
