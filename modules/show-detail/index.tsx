import { TabContainer } from '@/components/collapsing-tabs';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import CommunityTab from './components/CommunityTab';
import { TabItem } from '@/components/collapsing-tabs/TabItem';
import { ShowDetailHeader } from './components/ShowDetailHeader';
import EpisodesTab from './components/EpisodesTab';
import { View } from 'react-native';

type Props = {};

const ShowDetail = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <TabContainer
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        renderHeader={<ShowDetailHeader />}
      >
        <TabItem tabName='Community'>
          <CommunityTab />
        </TabItem>

        <TabItem tabName='Episodes'>
          <EpisodesTab />
        </TabItem>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top,
  },
  headerContainer: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  tabContainer: {
    overflow: 'hidden',
  },
}));

export default ShowDetail;
