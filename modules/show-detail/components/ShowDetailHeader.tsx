import {
  useAddPodcastRateMutation,
  useAddToWatchlistMutation,
  useDeletePodcastRateMutation,
  useGetMarkAsWatchQuery,
  useGetPodcastByIdQuery,
  useGetPodcastRateByPodcastIdQuery,
  useGetWatchListQuery,
  useLikePodcastMutation,
  useMarkAsWatchedMutation,
  useRemoveFromWatchlistMutation,
  useRemoveMarkAsWatchedMutation,
} from '@/apis/podcast';
import { Icons } from '@/assets/icons';
import StarRateFill from '@/assets/icons/start-rate-fill';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { useExpandableText } from '@/hooks/useExpandableText';
import { formatCompactNumber } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { memo, useCallback, useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import DeleteRateModal from './DeleteRateModal';
import JoinCommunityButton from './JoinCommunityButton';
import RatingModal from './RatingModal';
import ShowMenuModal from './ShowMenuModal';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import bigDecimal from 'js-big-decimal';
import { Spacer } from '@/components/Spacer';
import LikeShowButton from './LikeShowButton';
import RateButton from './RateButton';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { Skeleton } from 'moti/skeleton';

export const ShowDetailHeader = memo(() => {
  const localParams = useLocalSearchParams();
  const podcastId = localParams['podcastId'] as string;

  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [showDeleteRateModal, setShowDeleteRateModal] = useState(false);
  const [showRateModal, setShowRateModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { isExpanded, showButton, toggleExpanded, onTextLayout, numberOfLines } = useExpandableText({
    maxLines: 3,
  });

  const { data: podcast, isPending: isPendingPodcast } = useGetPodcastByIdQuery(
    { podcastId },
    { enabled: !!podcastId }
  );
  const { data: podcastRate, isPending: isPendingPodcastRate } = useGetPodcastRateByPodcastIdQuery(
    { podcastId },
    { enabled: !!podcastId, refetchOnMount: true }
  );
  const { data: watchList } = useGetWatchListQuery({ podcastId }, { enabled: !!podcastId });
  const { data: markAsWatchInfo } = useGetMarkAsWatchQuery({ podcastId }, { enabled: !!podcastId });
  const { mutateAsync: addRate, isPending: isPendingAddRate } = useAddPodcastRateMutation();
  const { mutateAsync: deleteRate, isPending: isPendingDeleteRate } = useDeletePodcastRateMutation();
  const { mutateAsync: addToWatchList, isPending: isPendingAddToWatchList } = useAddToWatchlistMutation();
  const { mutateAsync: removeFromWatchList, isPending: isPendingRemoveFromWatchList } =
    useRemoveFromWatchlistMutation();

  const { mutateAsync: markAsWatch, isPending: isPendingMarkAsWatch } = useMarkAsWatchedMutation();
  const { mutateAsync: unmarkAsWatch, isPending: isPendingUnmarkAsWatch } = useRemoveMarkAsWatchedMutation();

  const { mutateAsync: likePodcast } = useLikePodcastMutation();

  const handleBackPress = () => {
    router.back();
  };

  const handleMenuPress = useCallback(() => setShowMenuModal(true), []);

  const onDeleteRatePress = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteRateModal(true);
  };

  const handleRateSubmit = async (value: number) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await addRate({ podcastId: Number(podcastId), rate: value });
      setShowRateModal(false);
      refetchDiscoverPage();
      toastSuccess({ description: 'Rated show successfully' });
    } catch (error) {
      toastError(error);
    }
  };

  const handleAddToWatchlist = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) {
        setShowMenuModal(false);
        return;
      }

      if (watchList) {
        await removeFromWatchList(watchList.id);
        toastSuccess({ description: 'Removed from watchlist successfully!' });
      } else {
        await addToWatchList({
          podcastId,
        });
        toastSuccess({ description: 'Added to watchlist successfully!' });
      }

      setShowMenuModal(false);
    } catch (error) {
      toastError(error);
    }
  };

  const handleDeleteRate = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      if (!podcastRate) {
        toastError('Delete rate failed! Podcast has not been rated yet!');
        return;
      }
      await deleteRate({ id: podcastRate?.podcastId });
      toastSuccess({ description: 'Deleted rate successfully' });
      refetchDiscoverPage();
    } catch (error) {
      toastError(error);
    } finally {
      setShowDeleteRateModal(false);
    }
  };

  const refetchDiscoverPage = () => {
    Promise.all([
      queryClient.invalidateQueries({
        queryKey: queryKeys.podcasts.podcasts({
          limit: 5,
          page: 1,
          isTrending: true,
        }),
      }),
      queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.newestEpisodes() }),
    ]);
  };

  const handleMarkAsWatch = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      if (!markAsWatchInfo) {
        await markAsWatch({
          podcastId,
        });
        toastSuccess({ description: 'Marked as watched successfully!' });
      } else {
        await unmarkAsWatch(markAsWatchInfo.id);
        toastSuccess({ description: 'Unmarked as watched successfully!' });
      }
    } catch (error) {
      toastError(error);
    } finally {
      setShowMenuModal(false);
    }
  };

  const handleLike = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const isLiked = podcast?.hasLiked;
      await likePodcast(podcastId);
      toastSuccess({ description: isLiked ? 'Unliked show successfully' : 'Liked show successfully' });
    } catch (error) {
      toastError(error);
    }
  };

  const handleRatePress = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowRateModal(true);
  }, [onCheckAccountRestricted]);

  const handleEditRatePress = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowRateModal(true);
  };

  const handleInteract = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({ pathname: '/(app)/podcast/[podcastId]/add-post', params: { podcastId } });
  };

  const podcastImage = podcast?.imageUrl;
  const rateCount = Number(podcast?.rateCount || 0);
  const avgRate = bigDecimal.round(podcast?.avgRate, 1, bigDecimal.RoundingModes.HALF_UP);
  const userRate = bigDecimal.round(podcastRate?.rate, 1, bigDecimal.RoundingModes.HALF_UP);

  const formattedCommentCount = formatCompactNumber(rateCount);

  const isShowSkeleton = isPendingPodcast || isPendingPodcastRate;

  return (
    <View>
      <RatingModal
        isVisible={showRateModal}
        defaultRate={podcastRate?.rate}
        onClose={() => setShowRateModal(false)}
        onRateSubmit={handleRateSubmit}
        isAddingRate={isPendingAddRate}
        enjoyName={podcast?.title || ''}
        image={podcast?.imageUrl}
      />

      <ShowMenuModal
        isVisible={showMenuModal}
        onAddToWatchlist={handleAddToWatchlist}
        onUnmarkAsWatched={handleMarkAsWatch}
        isInWatchlist={!!watchList}
        isMarkedAsWatched={!!markAsWatchInfo}
        isPendingWatchlist={isPendingAddToWatchList || isPendingRemoveFromWatchList}
        isPendingMarkAsWatched={isPendingMarkAsWatch || isPendingUnmarkAsWatch}
        onClose={() => setShowMenuModal(false)}
      />

      <DeleteRateModal
        isDeleting={isPendingDeleteRate}
        isVisible={showDeleteRateModal}
        onClose={() => setShowDeleteRateModal(false)}
        onDeleteConfirm={handleDeleteRate}
      />

      <View style={styles.header}>
        <Pressable onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name='arrow-back' size={24} color='white' />
        </Pressable>

        <View style={styles.headerImageContainer} pointerEvents='none'>
          <ExpoImage source={{ uri: podcastImage }} style={styles.headerImage} contentFit='contain' />
        </View>
      </View>

      <View style={styles.showInfo}>
        {isShowSkeleton ? (
          <Skeleton width={150} height={28} />
        ) : (
          <ThemedText type='subtitleMedium' pointerEvents='box-none'>
            {podcast?.title?.trim()}
          </ThemedText>
        )}

        <Spacer height={3} />

        {isShowSkeleton ? (
          <Skeleton width={180} height={24} />
        ) : (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => router.push(`/(app)/podcast/${podcastId}/rating`)}
            style={styles.ratingContainer}
          >
            <StarRateFill style={styles.rateIcon} width={20} height={20} color={theme.colors.stateWarning} />

            <ThemedText style={styles.ratingNumber}>{avgRate}</ThemedText>

            <ThemedText style={styles.ratingText}>
              (Based on {formattedCommentCount} {Number(rateCount) > 1 ? 'reviews' : 'review'})
            </ThemedText>

            {podcastRate && podcastRate?.rate !== 0 && (
              <>
                <StarRateFill height={16} width={16} color={theme.colors.primary} style={styles.userRateIcon} />
                <ThemedText style={styles.userRateText}>{userRate}</ThemedText>
              </>
            )}
          </TouchableOpacity>
        )}

        <Spacer height={16} />

        <View style={styles.descriptionContainer}>
          {isShowSkeleton ? (
            <>
              <Skeleton width={'100%'} height={18} />
              <Spacer height={2} />
              <Skeleton width={'100%'} height={20} />
              <Spacer height={2} />
              <Skeleton width={'80%'} height={20} />
            </>
          ) : (
            <ThemedText style={styles.description} numberOfLines={numberOfLines} onTextLayout={onTextLayout}>
              {podcast?.description?.trim()}
            </ThemedText>
          )}

          {showButton && (
            <TouchableOpacity activeOpacity={0.7} onPress={toggleExpanded} style={styles.viewMoreButton}>
              <ThemedText style={styles.viewMoreText}>{isExpanded ? 'View Less' : 'View More'}</ThemedText>
              <Ionicons
                name={isExpanded ? 'chevron-up' : 'chevron-down'}
                size={16}
                color={theme.colors.neutralWhite}
                style={styles.dropdownIcon}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {isShowSkeleton ? (
        <View style={styles.actionButtons}>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <Skeleton width={38} height={38} radius={999} />
        </View>
      ) : (
        <View style={styles.actionButtons}>
          <JoinCommunityButton onPress={handleInteract} />

          <LikeShowButton isLiked={podcast?.hasLiked || false} onPress={handleLike} />

          <RateButton
            isRated={podcast?.hasRated || false}
            rating={podcastRate?.rate}
            onPress={handleRatePress}
            onDeleteRate={onDeleteRatePress}
            onEditRate={handleEditRatePress}
          />

          <Pressable style={styles.menuButton} onPress={handleMenuPress}>
            <Icons.EllipsisVertical size={16} color={theme.colors.neutralWhite} />
          </Pressable>
        </View>
      )}

      <Spacer height={24} />
    </View>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  header: {
    width: '100%',
    height: 200,
    position: 'relative',
    borderRadius: 12,
    marginBottom: 24,
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralBackground,
  },
  headerImageContainer: {
    width: '100%',
    height: '100%',
    // backgroundColor: theme.colors.neutralWhite,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    overflow: 'hidden',
    pointerEvents: 'none',
  },
  headerImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  content: {
    flex: 1,
  },
  showInfo: {
    marginBottom: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateIcon: {
    marginRight: 12,
  },
  userRateIcon: {
    marginLeft: 12,
  },
  userRateText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    marginLeft: 8,
  },
  ratingNumber: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    marginLeft: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#AAAAAA',
    marginLeft: 4,
  },
  descriptionContainer: {
    flexDirection: 'column',
  },
  description: {
    fontSize: 14,
    color: theme.colors.neutralWhite,
    opacity: 0.56,
    lineHeight: 20,
    pointerEvents: 'box-none',
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    gap: 3,
  },
  viewMoreText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    ...theme.fw500,
  },
  dropdownIcon: {
    marginLeft: 3,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 38,
    gap: 8,
  },
  menuButton: {
    width: 36,
    height: 36,
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF3D',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    height: 48,
    position: 'relative', // Added to allow the indicator to be positioned absolutely
    gap: 40,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabText: {
    fontSize: 16,
    ...theme.fw600,
    color: theme.colors.neutralWhite,
  },
  activeTabText: {
    color: theme.colors.neutralWhite,
  },
  inactiveTabText: {
    opacity: 0.56,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    height: 2,
    borderRadius: 2,
    backgroundColor: theme.colors.neutralWhite,
  },
  fullFlex: {
    flex: 1,
  },
}));
