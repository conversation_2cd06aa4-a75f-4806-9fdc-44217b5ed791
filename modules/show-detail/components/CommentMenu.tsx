import { useGetProfileQuery } from '@/apis/auth/queries';
import DeleteOutline from '@/assets/icons/delete-outline';
import EditOutline from '@/assets/icons/edit-outline';
import { ThemedText } from '@/components/ThemedText';
import { DropdownMenu, MenuOption } from '@/components/dropdown-menu';
import { Image } from 'expo-image';
import { PropsWithChildren } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CommentMenuProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onEditPost: () => void;
  onDeletePost: () => void;
}

function CommentMenu({ open, setOpen, onEditPost, onDeletePost, children }: PropsWithChildren<CommentMenuProps>) {
  const { styles, theme } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const isPremium = userProfile?.type === 'premium';

  return (
    <View>
      <DropdownMenu
        visible={open}
        handleOpen={() => setOpen(true)}
        handleClose={() => setOpen(false)}
        trigger={children}
      >
        <MenuOption
          onSelect={onEditPost}
          style={[styles.menuOption, !isPremium && styles.actionDisable]}
          disabled={!isPremium}
        >
          <EditOutline />

          <ThemedText style={styles.buttonText}>Edit Post</ThemedText>

          <Image style={{ width: 20, height: 20 }} source={require('@/assets/images/premium-icon.png')} />
        </MenuOption>

        <View style={styles.divider} />

        <MenuOption onSelect={onDeletePost} style={styles.menuOption}>
          <DeleteOutline color={theme.colors.stateError} />
          <ThemedText style={styles.deleteButtonText}>Delete Post</ThemedText>
        </MenuOption>
      </DropdownMenu>
    </View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  optionsContainerStyle: {
    marginTop: 52,
    backgroundColor: theme.colors.neutralDarkGrey,
    borderRadius: 8,
  },
  menuOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  buttonText: {
    marginLeft: 8,
    color: theme.colors.neutralWhite,
    ...theme.fw600,
    fontSize: 12,
    lineHeight: 12,
    marginRight: 8,
  },
  deleteButtonText: {
    marginLeft: 6,
    color: theme.colors.stateError,
    ...theme.fw600,
    lineHeight: 12,
    fontSize: 12,
  },
  divider: { height: 2, width: '100%', backgroundColor: theme.colors.neutralGrey },
  actionDisable: {
    opacity: 0.5,
  },
}));

export default CommentMenu;
