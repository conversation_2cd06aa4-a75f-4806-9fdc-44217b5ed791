import { useDeleteCommentMutation, useLikeCommentMutation } from '@/apis/comment/mutations';
import { ICommentCommunity } from '@/apis/comment/types';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { toastError, toastSuccess } from '@/utils/toast';
import { router } from 'expo-router';
import React, { memo, useMemo, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import CommentActions from './CommentAction';
import CommentImage from './CommentImage';
import CommentMenu from './CommentMenu';
import DeleteCommentModal from './DeleteCommentModal';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { UserProfileText } from '@/components/UserProfileText';
import { useIsYou } from '@/hooks/useIsYou';
import Tag from '@/components/Tag';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

interface CommentProps {
  comment: ICommentCommunity & { page?: number; limit?: number };
  podcastId?: string;
}

const Comment: React.FC<CommentProps> = memo(({ comment, podcastId }) => {
  const { styles, theme } = useStyles(stylesheet);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const isOwner = useIsYou({
    userId: comment.user.id.toString(),
  });

  const [openMenu, setOpenMenu] = React.useState(false);
  const [showDeleteCommentModal, setShowDeleteCommentModal] = useState(false);
  const { mutateAsync: deleteComment, isPending: isPendingDeleteComment } = useDeleteCommentMutation();
  const { mutateAsync: likeComment } = useLikeCommentMutation();

  const handlerDeleteComment = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await deleteComment({ id: comment.id });
      setShowDeleteCommentModal(false);
      toastSuccess({
        description: 'Deleted post successfully',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const usernameDisplay = useMemo(() => {
    if (isOwner) return 'You';

    return comment.user.username;
  }, [comment.user.username, isOwner]);

  const handleDirect = () => {
    router.push({
      pathname: '/(app)/podcast/[podcastId]/review/[postId]',
      params: {
        podcastId: podcastId || '',
        postId: comment.id,
        source: comment.source,
      },
    });
  };

  const handleLikeComment = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await likeComment({ id: comment.id, source: comment.source });
    } catch (error) {
      toastError(error);
    }
  };

  const handleEditPost = () => {
    setOpenMenu(false);
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push(`/(app)/podcast/${podcastId}/edit-post/${comment.id}`);
  };

  const handleConfirmDeletePost = () => {
    setOpenMenu(false);

    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteCommentModal(true);
  };

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={styles.container}>
      <UserProfileTouch userId={comment.user.id} userType={comment.source} style={styles.userImageContainer}>
        <Avatar image={comment.user.avatar} size={48} />
      </UserProfileTouch>

      <View style={styles.contentContainer}>
        <View style={{ paddingRight: 24 }}>
          <View style={styles.userBox}>
            <ThemedText style={styles.header} type='tinyMedium'>
              <UserProfileText userId={comment.user.id} userType={comment.source}>
                <ThemedText
                  type='tinyMedium'
                  style={[
                    isOwner
                      ? {
                          color: theme.colors.primary,
                        }
                      : null,
                  ]}
                >
                  {usernameDisplay}
                </ThemedText>
              </UserProfileText>{' '}
              on {comment.parentTitle}
            </ThemedText>

            <Show when={isOwner}>
              <DeleteCommentModal
                visible={showDeleteCommentModal}
                onClose={() => setShowDeleteCommentModal(false)}
                onDelete={handlerDeleteComment}
                isPendingDelete={isPendingDeleteComment}
              />

              <CommentMenu
                open={openMenu}
                setOpen={setOpenMenu}
                onEditPost={handleEditPost}
                onDeletePost={handleConfirmDeletePost}
              >
                <Icons.EllipsisVertical size={20} color={'#fff'} />
              </CommentMenu>
            </Show>
          </View>

          <Spacer height={8} />

          <Show when={comment.title?.trim().length > 0}>
            <ThemedText style={styles.title}>{comment.title?.trim()}</ThemedText>
          </Show>

          <Show when={comment.content?.trim().length > 0}>
            <ThemedText type='small' style={[styles.commentText]}>
              {comment.content}
              {comment.isEdited && (
                <ThemedText type='tinyMedium' style={styles.commentEditedText}>
                  {' '}
                  (Edited)
                </ThemedText>
              )}
            </ThemedText>
          </Show>
        </View>

        <Show when={comment.images.length > 0}>
          <>
            <Spacer height={16} />

            <CommentImage images={comment.images} />
          </>
        </Show>

        <Show when={!!comment.tag}>
          <Spacer height={16} />

          <Tag tagName={comment.tag} size={28} />
        </Show>

        <Spacer height={16} />

        <View style={{ paddingRight: 24 }}>
          <CommentActions
            comment={comment}
            onLikePress={handleLikeComment}
            onReplyPress={function (): void {}}
            onPress={function (): void {}}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
});

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    // paddingBottom: 20,
  },
  userImageContainer: {
    marginRight: 24,
  },
  userImage: {
    width: 48,
    height: 48,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralGrey,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    ...theme.fw500,
    lineHeight: 18,
    opacity: 0.56,
    color: theme.colors.neutralWhite,
    flex: 1,
  },
  title: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginBottom: 4,
  },
  commentText: {
    color: theme.colors.neutralWhite,
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw500,
  },
  imagesContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    flex: 1,
  },
  commentImage: {
    aspectRatio: 1,
    borderRadius: 8,
  },
  userBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  commentEditedText: {
    color: theme.colors.neutralLightGrey,
  },
}));

export default Comment;
