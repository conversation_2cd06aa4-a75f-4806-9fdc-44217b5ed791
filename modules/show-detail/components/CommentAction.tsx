import { ICommentResponse } from '@/apis/comment/types';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import { formatCompactNumber } from '@/utils/func';
import { Ionicons } from '@expo/vector-icons';
import { Pressable, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CommentActionsProps {
  comment: Pick<ICommentResponse, 'hasLiked' | 'likeCount' | 'replyCount'>;
  onLikePress: () => void;
  onReplyPress: () => void;
  onPress: () => void;
}

function CommentActions({ comment, onLikePress, onReplyPress, onPress }: CommentActionsProps) {
  const { styles, theme } = useStyles(stylesheet);
  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  return (
    <View style={styles.actions}>
      <Pressable
        onPress={onLikePress}
        style={styles.actionButton}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <Animated.View style={animatedStyle}>
          {comment.hasLiked ? (
            <Icons.HeartLikeFill size={18} />
          ) : (
            <Icons.HeartLikeOutline color={theme.colors.whiteOpacity56} size={18} />
          )}
        </Animated.View>

        <ThemedText style={[styles.actionText, comment.hasLiked ? { color: '#FF4287' } : null]}>
          {formatCompactNumber(Number(comment.likeCount || 0))}
        </ThemedText>
      </Pressable>

      <View style={styles.actionButton}>
        <Ionicons name='chatbox-outline' size={18} color={theme.colors.whiteOpacity56} />

        <ThemedText style={styles.actionText}>{formatCompactNumber(Number(comment.replyCount || 0))}</ThemedText>
      </View>
    </View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  actions: {
    flexDirection: 'row',
    gap: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    color: theme.colors.whiteOpacity56,
    marginLeft: 6,
    fontSize: 14,
  },
}));

export default CommentActions;
