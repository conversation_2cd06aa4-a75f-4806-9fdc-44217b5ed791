import { useEffect, useMemo, useState } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { useCheckUserProfileUpdateValidMutation, useUpdateUserProfileMutation } from '@/apis/user';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import { CODE_LENGTH } from '@/constants/common';
import useOtpCountDown from '@/hooks/useOtpCountDown';
import { toastError } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { router, useLocalSearchParams } from 'expo-router';
import {
  Cursor,
  CodeField as RNCodeField,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { SafeAreaView } from 'react-native-safe-area-context';
import queryKeys from '@/utils/queryKeys';

export const VerifyEmailProfile = () => {
  const localParams = useLocalSearchParams();

  const username = localParams?.username as string;
  const email = localParams?.email as string;
  const dateOfBirth = localParams?.dateOfBirth as string;
  const referral = localParams?.referral as string;
  const avatar = localParams?.avatar as string;
  const userId = localParams?.userId as string;
  const remainingTime = localParams?.remainingTime as string;
  const identifierType = email?.length > 0 ? 'email' : 'phone';

  const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({ value, cellCount: CODE_LENGTH });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
  const queryClient = useQueryClient();
  const { styles } = useStyles(stylesheet);

  const { mutateAsync: updateUserProfile, isPending: isUpdatingUserProfile } = useUpdateUserProfileMutation();
  const { mutateAsync: checkUserProfileUpdateValid, isPending: isCheckingUserProfileUpdateValid } =
    useCheckUserProfileUpdateValidMutation();

  const { handleCount, timeLeft } = useOtpCountDown();

  const handleContinue = async () => {
    if (value.length === CODE_LENGTH) {
      try {
        await updateUserProfile({
          avatar,
          email,
          dateOfBirth,
          userId: Number(userId),
          username,
          code: value,
        });
        await Promise.all([
          queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
          queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userId) }),
        ]);

        router.replace({
          pathname: '/choose-plan',
        });
      } catch (error) {
        toastError(error);
      }
    }
  };

  const handleResend = async () => {
    try {
      const { remainingTime } = await checkUserProfileUpdateValid({
        email,
        username,
        referral,
      });

      handleCount(Number(remainingTime) || 30);
    } catch (error) {
      toastError(error);
    }
  };

  const handleChangeIdentifier = () => {
    router.back();
  };

  const handleChangeCode = (value: string) => {
    setValue(value.replace(/[^0-9]/g, ''));
  };

  const timeLeftText = useMemo(() => {
    const secondsText = timeLeft < 10 ? `0${timeLeft}` : timeLeft;

    return timeLeft ? ` 00:${secondsText}` : '';
  }, [timeLeft]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (remainingTime) {
      handleCount(Number(remainingTime));
    }
  }, [remainingTime]);

  return (
    <SafeAreaView style={styles.container}>
      <Header isBack />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.title}>Enter Your Verification Code</ThemedText>

        <View style={styles.emailContainer}>
          <ThemedText style={styles.emailText}>
            We sent it to <ThemedText>{email}.</ThemedText>
          </ThemedText>
          <TouchableOpacity onPress={handleChangeIdentifier}>
            <ThemedText style={styles.changeIdentifierText}>
              Change {identifierType === 'email' ? 'Email' : 'Mobile Number'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <RNCodeField
          ref={ref}
          {...props}
          value={value}
          onChangeText={handleChangeCode}
          cellCount={CODE_LENGTH}
          rootStyle={styles.codeFieldRoot}
          keyboardType='number-pad'
          textContentType='oneTimeCode'
          renderCell={({ index, symbol, isFocused }) => (
            <View key={index} style={[styles.cell, isFocused && styles.focusCell]}>
              <ThemedText style={styles.cellText} onLayout={getCellOnLayoutHandler(index)}>
                {symbol || (isFocused ? <Cursor /> : null)}
              </ThemedText>
            </View>
          )}
        />

        <View style={styles.resendContainer}>
          <ThemedText style={styles.resendText}>Didn't receive a code? </ThemedText>
          <TouchableOpacity disabled={timeLeft > 0 || isCheckingUserProfileUpdateValid} onPress={handleResend}>
            <ThemedText style={[styles.resendLink, timeLeft > 0 && styles.disabledResendLink]}>
              Resend{timeLeftText}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <Button
          type='default'
          style={styles.continueButton}
          disabled={value.length !== CODE_LENGTH}
          onPress={handleContinue}
          isLoading={isUpdatingUserProfile}
        >
          Continue
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    marginBottom: 24,
    marginTop: 52,
  },
  emailContainer: {
    alignItems: 'center',
    marginBottom: 48,
    ...theme.fw500,
    fontSize: 14,
  },
  emailText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
    marginBottom: 8,
  },
  changeIdentifierText: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  codeFieldRoot: {
    width: '100%',
    marginBottom: 40,
    justifyContent: 'space-between',
  },
  cell: {
    width: 52,
    height: 52,
    borderRadius: 16,
    backgroundColor: theme.colors.neutralCard,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
  },
  focusCell: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  cellText: {
    fontSize: 20,
    lineHeight: 24,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 74,
  },
  resendText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
  },
  resendLink: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  disabledResendLink: {
    opacity: 0.5,
  },
  continueButton: {
    marginHorizontal: 31,
  },
}));
