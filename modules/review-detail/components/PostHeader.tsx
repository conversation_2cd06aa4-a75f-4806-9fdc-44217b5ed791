import { useLikeCommentMutation, useLikeEpisodeCommentMutation } from '@/apis/comment/mutations';
import { ICommentCommunity } from '@/apis/comment/types';
import { IEpisode, IPodcast } from '@/apis/podcast';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { ImagesPreview } from '@/components/ui/ImagesPreview';
import CommentActions from '@/modules/show-detail/components/CommentAction';
import { toastError } from '@/utils/toast';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { FlatList, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PostHeaderSkeleton } from './PostHeaderSkeleton';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { Show } from '@/components/Show';
import Tag from '@/components/Tag';
import { RateStar } from '@/components/RateStar';
import { ExpoImage } from '@/components/ui/Image';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  postInfo?: ICommentCommunity;
  podcastInfo?: IPodcast;
  episodeInfo?: IEpisode;
  isLoading: boolean;
  isPostOwner: boolean;
  userRate: number;
};

export const PostHeader = ({ podcastInfo, episodeInfo, postInfo, isLoading, isPostOwner, userRate }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const contentInfo = podcastInfo || episodeInfo;
  const isEpisode = !!episodeInfo;

  // Use appropriate like mutation based on content type
  const { mutateAsync: likePodcastComment } = useLikeCommentMutation();
  const { mutateAsync: likeEpisodeComment } = useLikeEpisodeCommentMutation();

  // Choose the appropriate like function based on content type
  const likeComment = isEpisode ? likeEpisodeComment : likePodcastComment;

  const handleDirectPodcastDetail = () => {
    if (podcastInfo) {
      return showDetailDirect(queryClient, podcastInfo.id.toString());
    }

    if (episodeInfo) {
      return episodeDetailDirect(queryClient, episodeInfo.id.toString());
    }
  };

  const handleLikeComment = async () => {
    if (!postInfo) return;

    try {
      await likeComment({ id: postInfo.id, source: postInfo.source });
    } catch (error) {
      toastError(error);
    }
  };

  const usernameDisplay = useMemo(() => {
    if (isPostOwner) return 'You';

    return postInfo?.user?.username;
  }, [postInfo?.user?.username, isPostOwner]);

  if (isLoading || !contentInfo || !postInfo) return <PostHeaderSkeleton />;

  const postTimeFormatTime = format(new Date(postInfo?.updatedAt || postInfo?.createdAt), 'hh:mm a');
  const postTimeFormatDate = format(new Date(postInfo?.updatedAt || postInfo?.createdAt), 'dd MMM yyyy');
  const watchedDate = podcastInfo?.watched?.createdAt || episodeInfo?.watched?.createdAt;

  const watchedFormatDate = watchedDate ? format(new Date(watchedDate), 'dd MMM yyyy') : '';

  return (
    <View style={{ pointerEvents: 'auto' }}>
      <View style={styles.info}>
        <View style={styles.podcastInfo}>
          <UserProfileTouch userId={postInfo?.user?.id} userType={postInfo?.source} style={styles.posterInfoBox}>
            <Avatar image={postInfo?.user?.avatar} size={48} />

            <ThemedText type='small' style={[{ flex: 1 }, isPostOwner && styles.textPrimary]}>
              {usernameDisplay}
            </ThemedText>
          </UserProfileTouch>

          <ThemedText type='default'>{contentInfo?.title}</ThemedText>

          {userRate > 0 && (
            <View>
              <RateStar rating={Number(userRate || 0)} size={16} gap={4} />
            </View>
          )}

          {watchedFormatDate && (
            <ThemedText type='small' style={styles.time}>
              Watched {watchedFormatDate}
            </ThemedText>
          )}
        </View>

        <TouchableOpacity activeOpacity={0.7} onPress={handleDirectPodcastDetail}>
          <ExpoImage source={{ uri: contentInfo?.imageUrl }} style={styles.podcastImage} />
        </TouchableOpacity>
      </View>

      <Spacer height={24} />

      <View style={styles.reviewBox}>
        <ThemedText type='default'>{postInfo?.title?.trim()}</ThemedText>

        <ThemedText type='small'>{postInfo?.content?.trim()}</ThemedText>

        <FlatList
          data={postInfo.images}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => (item + index).toString()}
          renderItem={({ item, index }) => (
            <ImagesPreview currentIndex={index} images={postInfo.images} source={{ uri: item }} style={styles.image} />
          )}
        />

        <Show when={!!postInfo?.tag}>
          <Tag tagName={postInfo?.tag} size={32} />
        </Show>

        <View style={styles.reviewTimeBox}>
          <ThemedText type='small' style={styles.reviewTimeText}>
            {postTimeFormatTime}
          </ThemedText>

          <ThemedText type='small' style={styles.reviewTimeText}>
            {postTimeFormatDate}
          </ThemedText>
        </View>
      </View>

      <Spacer height={24} />

      <CommentActions
        comment={{
          hasLiked: postInfo?.hasLiked,
          likeCount: postInfo?.likeCount,
          replyCount: postInfo?.replyCount,
        }}
        onLikePress={handleLikeComment}
        onReplyPress={function (): void {}}
        onPress={function (): void {}}
      />

      <Spacer height={32} />

      <View style={styles.bottomLine} />

      <Spacer height={24} />

      <ThemedText type='defaultMedium'>Newest Reply</ThemedText>

      <Spacer height={24} />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    height: 32,
  },
  avatarPoster: {
    width: 48,
    height: 48,
    borderRadius: 999,
  },
  posterInfoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    width: '100%',
  },
  podcastImage: {
    width: 120,
    height: 120,
    borderRadius: 5.12,
  },
  info: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 20,
  },
  podcastInfo: {
    gap: 12,
    alignItems: 'flex-start',
    flex: 1,
  },
  podcastTitle: {},
  time: {
    opacity: 0.56,
  },
  reviewTimeBox: {
    flexDirection: 'row',
    gap: 20,
  },
  reviewTimeText: {
    color: theme.colors.neutralLightGrey,
  },
  reviewBox: {
    gap: 16,
  },
  bottomLine: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.neutralGrey,
  },
  image: {
    width: 187,
    height: 187,
    borderRadius: 16,
    marginRight: 12,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
}));
