import { useDeleteReplyCommentEpisodeMutation, useDeleteReplyCommentMutation } from '@/apis/comment/mutations';
import { ICommentReply, ICommentResponse } from '@/apis/comment/types';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { useIsYou } from '@/hooks/useIsYou';
import DeleteCommentModal from '@/modules/show-detail/components/DeleteCommentModal';
import { formatIntervalToNowDuration } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { UserProfileText } from '@/components/UserProfileText';
import { Show } from '@/components/Show';
import CommentMenu from '@/modules/show-detail/components/CommentMenu';

type Props = {
  postInfo?: ICommentResponse;
  commentData: ICommentReply;
  podcastId?: string;
  episodeId?: string;
  onRefetch: () => void;
};

export const Comment = ({ commentData, postInfo, podcastId, episodeId, onRefetch }: Props) => {
  const { user, content, updatedAt, id } = commentData;
  const { styles, theme } = useStyles(stylesheet);
  const [openMenu, setOpenMenu] = useState(false);
  const [showDeleteCommentModal, setShowDeleteCommentModal] = useState(false);

  const isOwner = useIsYou({
    userId: commentData.user.id.toString(),
  });

  const queryClient = useQueryClient();

  const { mutateAsync: deleteComment, isPending: isPendingDeleteComment } = useDeleteReplyCommentMutation();

  const { mutateAsync: deleteCommentEpisode, isPending: isPendingDeleteCommentEpisode } =
    useDeleteReplyCommentEpisodeMutation();

  const handlerDeleteComment = async () => {
    if (!postInfo) return;
    try {
      if (episodeId) {
        await deleteCommentEpisode({
          id: commentData.id,
          source: postInfo?.source,
        });
      } else {
        await deleteComment({
          id: commentData.id,
          source: postInfo?.source,
        });
      }
      setShowDeleteCommentModal(false);
      toastSuccess({
        description: 'Deleted post successfully',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleConfirmDeletePost = (isShow: boolean) => {
    setShowDeleteCommentModal(isShow);
  };

  const handleToggleMenuDelete = () => {
    handleConfirmDeletePost(true);
    setOpenMenu(false);
  };

  const handleEditPost = () => {
    setOpenMenu(false);

    if (!postInfo) return; // Don't navigate if postInfo is not available

    if (episodeId) {
      // Route for episode comment reply
      router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]/reply',
        params: {
          episodeId,
          postId: postInfo?.id?.toString() ?? '',
          isEdit: 'true',
          postInfo: JSON.stringify(postInfo),
          initCommentContent: content,
          commentId: id,
        },
      });
    } else if (podcastId) {
      // Route for podcast comment reply
      router.push({
        pathname: '/(app)/podcast/[podcastId]/review/[postId]/reply',
        params: {
          podcastId,
          postId: postInfo?.id?.toString() ?? '',
          isEdit: 'true',
          postInfo: JSON.stringify(postInfo),
          initCommentContent: content,
          commentId: id,
        },
      });
    }
  };

  return (
    <View style={styles.container}>
      <UserProfileTouch userId={user?.id} userType={postInfo?.source}>
        <Avatar image={user?.avatar} size={40} />
      </UserProfileTouch>

      <View style={styles.commentBox}>
        <View style={styles.headerComment}>
          <UserProfileText userId={user?.id} userType={postInfo?.source}>
            <ThemedText type='tinyMedium' style={[styles.textOpacity, isOwner && styles.textPrimary]}>
              {isOwner ? 'You' : user?.username}
            </ThemedText>
          </UserProfileText>

          <View style={styles.rowCenter}>
            <ThemedText type='tinyMedium' style={styles.textOpacity}>
              {formatIntervalToNowDuration(updatedAt)}
            </ThemedText>

            <Show when={isOwner}>
              <CommentMenu
                open={openMenu}
                setOpen={setOpenMenu}
                onDeletePost={handleToggleMenuDelete}
                onEditPost={handleEditPost}
              >
                <Icons.EllipsisVertical size={24} color={theme.colors.neutralWhite} />
              </CommentMenu>
            </Show>
          </View>
        </View>

        <ThemedText type='small'>
          {content}
          {commentData.updatedAt !== commentData.createdAt && (
            <ThemedText type='tinyMedium' style={styles.commentEditedText}>
              {' '}
              (Edited)
            </ThemedText>
          )}
        </ThemedText>
      </View>

      <DeleteCommentModal
        visible={showDeleteCommentModal}
        onClose={() => handleConfirmDeletePost(false)}
        onDelete={handlerDeleteComment}
        isPendingDelete={isPendingDeleteComment}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerComment: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  container: {
    flexDirection: 'row',
    gap: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 999,
  },
  info: {
    gap: 16,
  },
  textOpacity: {
    opacity: 0.56,
  },
  commentBox: {
    flex: 1,
    gap: 4,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  commentEditedText: {
    color: theme.colors.neutralLightGrey,
  },
}));
