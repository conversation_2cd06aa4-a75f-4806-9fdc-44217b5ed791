import { TabBarCollapsibleTabView } from '@/components/TabBarCollapsibleTabView';
import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikesEpisodes } from './components/tabs/LikesEpisodes';
import { LikesShow } from './components/tabs/LikesShow';
import { useLocalSearchParams } from 'expo-router';
import { LikesPost } from './components/tabs/LikesPost';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';

export const AccountLikes = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title={isPendingUserProfile ? '' : isYou ? 'My Likes' : `${userProfileById?.username} Likes`} isBack />
      </View>

      <Tabs.Container
        containerStyle={styles.tabContainer}
        initialTabName='Show'
        renderTabBar={(TabsProps) => <TabBarCollapsibleTabView {...TabsProps} />}
      >
        <Tabs.Tab name='Show' label={'Show'}>
          <LikesShow userId={userId} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name='Episodes' label={'Episodes'}>
          <LikesEpisodes userId={userId} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name='Post' label={'Post'}>
          <LikesPost userId={userId} profile={userProfileById} />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
}));
