import { IUserProfileById, UserLikesEpisode } from '@/apis/user';
import { useGetInfiniteUserLikesEpisodesHistoryQuery } from '@/apis/user/queries';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ExpoImage } from '@/components/ui/Image';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { ListRenderItem, TouchableOpacity } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { useAnimatedScrollHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

const itemSize = getItemSizeFlatList(24, 3, 11);

export const LikesEpisodes = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const [isEndReachedCalled, setIsEndReachedCalled] = useState(false);

  const {
    data: likesEpisodesData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetInfiniteUserLikesEpisodesHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesEpisodes = likesEpisodesData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserLikesEpisode) => {
      episodeDetailDirect(queryClient, item.episodeId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserLikesEpisode>>(
    ({ item }) => (
      <TouchableOpacity activeOpacity={0.7} onPress={() => handleDirect(item)}>
        <ExpoImage source={{ uri: item.imageUrl }} style={{ width: itemSize, height: itemSize, borderRadius: 8 }} />
      </TouchableOpacity>
    ),
    [handleDirect]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, isEndReachedCalled, fetchNextPage]);

  const onStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage && !isEndReachedCalled) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, isEndReachedCalled, fetchPreviousPage]);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const keyExtractor = useCallback((item: UserLikesEpisode) => item.id.toString(), []);

  const handler = useAnimatedScrollHandler({});

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const isShowEmpty = !isFetching && likesEpisodes.length === 0;

  return (
    <Tabs.FlatList
      bounces={false}
      numColumns={3}
      data={likesEpisodes}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={[styles.contentContainer, isShowEmpty ? { justifyContent: 'center' } : {}]}
      columnWrapperStyle={styles.columnWrapper}
      maxToRenderPerBatch={24}
      updateCellsBatchingPeriod={50}
      scrollEventThrottle={16}
      initialNumToRender={24}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onStartReached={onStartReached}
      onStartReachedThreshold={0.4}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      onScroll={handler}
      onMomentumScrollEnd={() => {}}
      onMomentumScrollBegin={handleMomentumScrollBegin}
      ListHeaderComponent={isFetchingPreviousPage ? renderSkeleton : null}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any episodes`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    marginBottom: 24,
    paddingHorizontal: 24,
    gap: 28,
  },
  columnWrapper: {
    gap: 11,
  },
}));
