import { IUserProfileById, UserLikesPost } from '@/apis/user';
import { useGetInfiniteUserLikesPostsHistoryQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { memo, useCallback, useState } from 'react';
import { ListRenderItem, View } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { useAnimatedScrollHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikedPostItem } from '../LikedPostItem';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

export const LikesPost = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const [isEndReachedCalled, setIsEndReachedCalled] = useState(false);

  const {
    data: likesPostData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetInfiniteUserLikesPostsHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPost = likesPostData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<UserLikesPost>>(
    ({ item }) => <LikedPostItem likedPostItem={item} />,
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, isEndReachedCalled, fetchNextPage]);

  const onStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage && !isEndReachedCalled) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, isEndReachedCalled, fetchPreviousPage]);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const keyExtractor = useCallback((item: UserLikesPost) => `${item.parentId}-${item.commentId}`, []);

  const handler = useAnimatedScrollHandler({});

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderItemSeparator = useCallback(() => <View style={styles.line} />, [styles.line]);

  const isShowEmpty = !isFetching && likesPost.length === 0;

  return (
    <Tabs.FlatList
      bounces={false}
      data={likesPost}
      style={styles.container}
      contentContainerStyle={[styles.contentContainer, isShowEmpty ? { justifyContent: 'center' } : {}]}
      showsVerticalScrollIndicator={false}
      // maxToRenderPerBatch={10}
      // updateCellsBatchingPeriod={50}
      // scrollEventThrottle={16}
      // initialNumToRender={10}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onStartReached={onStartReached}
      onStartReachedThreshold={0.4}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      onScroll={handler}
      onMomentumScrollEnd={() => {}}
      onMomentumScrollBegin={handleMomentumScrollBegin}
      ListHeaderComponent={isFetchingPreviousPage ? renderSkeleton : null}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ItemSeparatorComponent={renderItemSeparator}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any posts`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 28,
  },
  line: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    marginTop: 24,
  },
}));
