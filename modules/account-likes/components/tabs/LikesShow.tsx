import { IUserProfileById, UserLikesPodcast } from '@/apis/user';
import { useGetInfiniteUserLikesPodcastsHistoryQuery } from '@/apis/user/queries';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ExpoImage } from '@/components/ui/Image';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { ListRenderItem, TouchableOpacity } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { useAnimatedScrollHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

export const LikesShow = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const [isEndReachedCalled, setIsEndReachedCalled] = useState(false);

  const {
    data: likesPodcastsData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetInfiniteUserLikesPodcastsHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPodcasts = likesPodcastsData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserLikesPodcast) => {
      showDetailDirect(queryClient, item.podcastId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserLikesPodcast>>(
    ({ item }) => (
      <TouchableOpacity onPress={() => handleDirect(item)} activeOpacity={0.7}>
        <ExpoImage source={{ uri: item.imageUrl }} style={{ width: itemSize, height: itemSize, borderRadius: 8 }} />
      </TouchableOpacity>
    ),
    [handleDirect]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, isEndReachedCalled, fetchNextPage]);

  const onStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage && !isEndReachedCalled) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, isEndReachedCalled, fetchPreviousPage]);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const keyExtractor = useCallback((item: UserLikesPodcast) => item.id.toString(), []);

  const handler = useAnimatedScrollHandler({});

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const isShowEmpty = !isFetching && likesPodcasts.length === 0;

  return (
    <Tabs.FlatList
      bounces={false}
      numColumns={3}
      data={likesPodcasts}
      style={styles.container}
      contentContainerStyle={[styles.contentContainer, isShowEmpty ? { justifyContent: 'center' } : {}]}
      columnWrapperStyle={styles.columnWrapper}
      showsVerticalScrollIndicator={false}
      maxToRenderPerBatch={24}
      updateCellsBatchingPeriod={50}
      scrollEventThrottle={16}
      initialNumToRender={24}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onStartReached={onStartReached}
      onStartReachedThreshold={0.4}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      onScroll={handler}
      onMomentumScrollEnd={() => {}}
      onMomentumScrollBegin={handleMomentumScrollBegin}
      ListHeaderComponent={isFetchingPreviousPage ? renderSkeleton : null}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any shows`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    marginBottom: 24,
    paddingHorizontal: 24,
    gap: 28,
  },
  columnWrapper: {
    gap: 11,
  },
}));
