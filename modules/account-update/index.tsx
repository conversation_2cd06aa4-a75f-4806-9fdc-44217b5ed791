import { useGetProfileQuery } from '@/apis/auth/queries';
import { useUpdateUserProfileMeMutation, useUploadImageToS3, useUploadUserImageMutation } from '@/apis/user/mutations';
import { Header } from '@/components/ui/Header';
import { UpdateUserProfileZodData, UpdateUserProfileZodSchema } from '@/lib/validations/auth';
import { convertDateFormat } from '@/utils/dateUtils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { ImagePickerAsset } from 'expo-image-picker';
import { router } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ScrollView, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ProfileBioSection } from './components/ProfileBioSection';
import { ProfileFormSection } from './components/ProfileFormSection';
import { ProfileImageSection } from './components/ProfileImageSection';
import { SaveButton } from './components/SaveButton';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { formatDate } from 'date-fns';
import { DEFAULT_CODE_PENDING } from '@/constants/common';
import { toastError } from '@/utils/toast';
import { useRequestUpdateInforVerificationMutation } from '@/apis/auth';
import queryKeys from '@/utils/queryKeys';
import { useGetRegionInfoQuery } from '@/apis/user/queries';
import { phoneTransform } from '@/utils/phoneUtils';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

const AccountUpdate = () => {
  const { theme, styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [profileImage, setProfileImage] = useState<ImagePickerAsset>();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const { data: userProfile } = useGetProfileQuery();
  const [identifierType, setIdentifierType] = useState<string[]>([]);
  const { data: regionInfo } = useGetRegionInfoQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<UpdateUserProfileZodData>({
    defaultValues: {
      username: userProfile?.username || '',
      email: userProfile?.email || '',
      dateOfBirth: userProfile?.dateOfBirth || '',
      bio: userProfile?.bio || '',
      phoneNumber: userProfile?.phoneNumber || '',
    },
    resolver: zodResolver(UpdateUserProfileZodSchema),
  });

  const { mutateAsync: updateUserProfile, isPending: isUpdatingUserProfile } = useUpdateUserProfileMeMutation();
  const { mutateAsync: uploadUserImage, isPending: isUploadingUser } = useUploadUserImageMutation();
  const { mutateAsync: uploadImageToS3, isPending: isUploadingToS3 } = useUploadImageToS3();
  const { mutateAsync: requestVerification, isPending: isRequestingVerification } =
    useRequestUpdateInforVerificationMutation();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (userProfile) {
      setValue('username', userProfile.username || '');
      setValue('email', userProfile.email || '');
      setValue('dateOfBirth', formatDate(userProfile.dateOfBirth, 'MM/dd/yyyy') || '');
      setValue('bio', userProfile.bio || '');
      setValue('phoneNumber', userProfile.phoneNumber || undefined);
    }
  }, [userProfile]);

  const isLoadingSubmit = useMemo(
    () => isUpdatingUserProfile || isUploadingUser || isUploadingToS3,
    [isUpdatingUserProfile, isUploadingUser, isUploadingToS3]
  );

  const onSubmit = async (data: UpdateUserProfileZodData) => {
    let profileImageUrl: string = '';

    if (profileImage) {
      const uploadUserImageRes = await uploadUserImage({
        file: profileImage,
      });

      if (uploadUserImageRes?.presigned) {
        await uploadImageToS3({
          file: profileImage.uri,
          url: uploadUserImageRes?.presigned,
        });
      }

      profileImageUrl = uploadUserImageRes?.url;
    }

    let identifierType: string[] = [];

    try {
      if (!userProfile?.id) return;

      const isRestricted = onCheckAccountRestricted();
      ({ isRestricted });
      if (isRestricted) return;

      const phoneNumber = data.phoneNumber ? phoneTransform(data.phoneNumber, regionInfo?.calling_code) : undefined;
      const { data: res } = await updateUserProfile({
        username: data.username,
        email: data.email,
        dateOfBirth: convertDateFormat(data.dateOfBirth),
        bio: data?.bio,
        avatar: profileImageUrl,
        userId: userProfile?.id,
        phoneNumber: phoneNumber || undefined,
      });

      if (res?.isEmailChanged && data.email) {
        identifierType.push(data.email);
      }

      if (res?.isPhoneChanged && phoneNumber) {
        identifierType.push(phoneNumber);
      }

      setIdentifierType(identifierType);

      if (identifierType.length > 0) {
        setShowConfirmationModal(true);
      } else {
        await Promise.all([
          queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
          queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
        ]);
        router.back();
      }
    } catch (error) {
      toastError(error);
    }
  };

  const handleModalCancel = () => {
    setShowConfirmationModal(false);
  };

  const handleConfirmChanges = async () => {
    try {
      if (identifierType.length > 0) {
        const remainingTime = 30;

        setShowConfirmationModal(false);
        router.push({
          pathname: '/(app)/verify-code',
          params: {
            identifierType: JSON.stringify(identifierType),
            remainingTime: Number(remainingTime || DEFAULT_CODE_PENDING),
            isProfileUpdate: 'true',
          },
        });
        await requestVerification({
          identifier: identifierType[0],
        });
        return;
      }

      await Promise.all([
        queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
      ]);
      setShowConfirmationModal(false);
      router.back();
    } catch (error) {
      toastError(error);
      setShowConfirmationModal(false);
    }
  };

  return (
    <KeyboardAvoidingView style={{ flex: 1 }} behavior={'padding'} keyboardVerticalOffset={20}>
      <SafeAreaView style={styles.container}>
        <Header title='Edit Profile' isBack titleStyle={styles.title} />

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <ProfileImageSection
              imageUri={profileImage?.uri || userProfile?.avatar || ''}
              onImageChange={setProfileImage}
            />

            <ProfileFormSection control={control} errors={errors} userProfile={userProfile} />

            <ProfileBioSection control={control} errors={errors} />
          </View>
          <SaveButton onPress={handleSubmit(onSubmit)} isLoading={isLoadingSubmit} />
        </ScrollView>

        <ConfirmationModal
          isVisible={showConfirmationModal}
          onClose={handleModalCancel}
          onCancel={handleModalCancel}
          onConfirm={handleConfirmChanges}
          title='Are you sure you want to update these information?'
          description='Updating email or phone number may affect how you sign in to your account'
          cancelText='Cancel'
          confirmText='Edit Profile'
          isLoading={isLoadingSubmit}
        />
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    gap: 20,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    lineHeight: 32,
  },
}));

export default AccountUpdate;
