import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { CustomButton } from '@/components/ui/CustomButton';
import { router } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountVerify } from './AccountVerify';
import { CountNumber } from './CountNumber';
import { IUserProfileById, useToggleFollowUserMutation } from '@/apis/user';
import { toastError } from '@/utils/toast';
import { useIsYou } from '@/hooks/useIsYou';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  isPatron: boolean;
  isYouTab: boolean;
  profile?: IUserProfileById;
  onChangeTab: (tabIndex: number) => void;
};

export const ProfileInfo = ({ isPatron, isYouTab, profile, onChangeTab }: Props) => {
  const { styles } = useStyles(stylesheet);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const isYou = useIsYou({
    userId: profile?.id?.toString() ?? '',
  });

  const { mutateAsync: toggleFollowUser, isPending: isTogglingFollowUser } = useToggleFollowUserMutation();

  const accountType = isPatron ? 'Founding Patron' : 'Founding Member';

  const handleDirectFollowPage = (tab: 'followers' | 'following') => {
    router.push({
      pathname: '/(app)/profile-followers',
      params: {
        userId: profile?.id?.toString(),
        username: profile?.username,
        initialTab: tab,
      },
    });
  };

  const handleToggleFollowUser = async () => {
    if (!profile?.id) return;

    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await toggleFollowUser(profile?.id);
    } catch (error) {
      toastError(error);
    }
  };

  const totalComments = Number(profile?.totalComments || 0);
  const totalFollowers = Number(profile?.followersCount || 0);
  const totalFollowing = Number(profile?.followingCount || 0);

  return (
    <View>
      <Avatar size={100} image={profile?.avatar} containerStyle={styles.avatarContainer} />

      <View style={styles.container}>
        <Spacer height={28} />

        <View style={styles.usernameContainer}>
          <View style={styles.usernameBox}>
            <View>
              <ThemedText type='subtitleSemiBold' style={[styles.username, styles.textCenter]}>
                {profile?.username}
              </ThemedText>

              <Show when={isPatron}>
                <View style={styles.verifyBox}>
                  <AccountVerify isVerified isYouTab={isYouTab} />
                </View>
              </Show>
            </View>

            <Show when={isYouTab && !isPatron}>
              <View style={styles.verifyContainerBottom}>
                <AccountVerify isVerified={isPatron} isYouTab={isYouTab} />
              </View>
            </Show>
          </View>
        </View>

        <Spacer height={12} />

        <View style={styles.accountTypeContainer} pointerEvents='none'>
          <ThemedText style={styles.accountTypeText}>{accountType}</ThemedText>
        </View>

        <Spacer height={28} />

        <View style={styles.countNumberContainer}>
          <View style={styles.boxCount}>
            <CountNumber
              label={totalComments > 1 ? 'Posts' : 'Post'}
              count={totalComments}
              onPress={() => onChangeTab(1)}
            />
          </View>

          <View style={[styles.boxCount, styles.centerBoxCount]}>
            <CountNumber
              label={totalFollowers > 1 ? 'Followers' : 'Follower'}
              count={totalFollowers}
              onPress={() => handleDirectFollowPage('followers')}
            />
          </View>

          <View style={styles.boxCount}>
            <CountNumber label='Following' count={totalFollowing} onPress={() => handleDirectFollowPage('following')} />
          </View>
        </View>

        <Show when={!isYouTab && !profile?.isMe && !isYou}>
          <Spacer height={28} />

          <CustomButton
            activeOpacity={0.7}
            style={styles.followBtn}
            textType='defaultBold'
            disabled={!profile?.id || isTogglingFollowUser}
            isLoading={isTogglingFollowUser}
            onPress={handleToggleFollowUser}
            type={profile?.isFollowed ? 'primaryOpacity10' : 'primary'}
          >
            {profile?.isFollowed ? 'Following' : 'Follow'}
          </CustomButton>
        </Show>

        <Show when={!!profile?.bio}>
          <Spacer height={28} />

          <ThemedText type='tiny' style={styles.textCenter} pointerEvents='none'>
            {profile?.bio}
          </ThemedText>
        </Show>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  usernameBox: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
  verifyBox: {
    position: 'absolute',
    right: 0,
    top: '50%',
    paddingLeft: 12,
    transform: [{ translateX: '100%' }, { translateY: '-50%' }],
  },
  container: {
    paddingHorizontal: 24,
    paddingTop: 50,
    flexDirection: 'column',
    alignItems: 'center',
  },
  usernameContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  verifyContainer: {
    position: 'absolute',
    right: 0,
    top: '50%',
    paddingLeft: 12,
    transform: [{ translateX: '100%' }, { translateY: '-50%' }],
  },
  verifyContainerBottom: {
    marginTop: 8,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'absolute',
    top: -50,
    left: '50%',
    transform: [{ translateX: '-50%' }],
  },
  accountTypeContainer: {
    backgroundColor: theme.colors.neutralCard,
    paddingHorizontal: 16,
    borderRadius: 999,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
  },
  accountTypeText: {
    ...theme.fw600,
    fontSize: 10,
    lineHeight: 22,
    color: theme.colors.primary,
  },
  countNumberContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  centerBoxCount: {
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#FEFFFB1A',
  },
  boxCount: {
    width: 100,
  },
  followBtn: {
    width: '100%',
    maxWidth: 240,
  },
  textCenter: {
    textAlign: 'center',
  },
  username: {
    maxWidth: '90%',
  },
}));
