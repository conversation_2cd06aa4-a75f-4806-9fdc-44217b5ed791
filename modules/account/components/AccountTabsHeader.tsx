import { Spacer } from '@/components/Spacer';
import { memo, useMemo } from 'react';
import { View } from 'react-native';
import Animated, { interpolate, interpolateColor, useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountHeader } from './AccountHeader';
import { ProfileHeader } from './ProfileHeader';
import { ProfileInfo } from './ProfileInfo';
import { Show } from '@/components/Show';
import { ProfileSkeleton } from './ProfileSkeleton';
import { IUserProfileById } from '@/apis/user';
import { useCollapsingTabsContext } from '@/contexts/app.context';

const HEADER_HEIGHT = 58;
const MAX_PULL_DISTANCE = 150;

type Props = {
  isYouTab: boolean;
  isLoading: boolean;
  profile?: IUserProfileById;
};

export const AccountTabsHeader = memo(({ isYouTab, isLoading, profile }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const insets = useSafeAreaInsets();
  const { refPager, currentScroll: scrollY, headerDiff: height } = useCollapsingTabsContext();

  const headerStyle = useAnimatedStyle(() => {
    const maxScroll = height;

    return {
      transform: [
        {
          translateY: interpolate(scrollY.value, [0, maxScroll], [0, maxScroll], 'clamp'),
        },
      ],
      backgroundColor: interpolateColor(
        scrollY.value,
        [0, insets.top],
        ['transparent', theme.colors.neutralBackground]
      ),
    };
  });

  const isPatron = useMemo(() => {
    if (!profile) return false;
    return profile?.type === 'premium';
  }, [profile]);

  const onChangeTab = (pageIndex: number) => {
    refPager.current?.setPage(pageIndex);
  };

  return (
    <Animated.View style={styles.container} pointerEvents='box-none'>
      <Animated.View style={[styles.headerScreenContainer, headerStyle]} pointerEvents={'box-none'}>
        <AccountHeader isYouTab={isYouTab} />
      </Animated.View>

      <Show when={isLoading}>
        <ProfileSkeleton />
      </Show>

      <Show when={!isLoading}>
        <View pointerEvents={'box-none'}>
          <ProfileHeader isYouTab={isYouTab} isPatron={isPatron} />
          <ProfileInfo isPatron={isPatron} isYouTab={isYouTab} profile={profile} onChangeTab={onChangeTab} />
        </View>
      </Show>

      <Spacer height={24} />
    </Animated.View>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  headerScreenContainer: {
    paddingTop: rt.insets.top + 10,
    paddingHorizontal: 24,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    paddingBottom: 10,
    zIndex: 999,
  },
  refreshContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: HEADER_HEIGHT,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  refreshIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
}));
