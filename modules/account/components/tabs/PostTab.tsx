import { memo, useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountPostItem } from '../post/AccountPostItem';
import { useGetInfinitePostHistoryQuery } from '@/apis/user/queries';
import { IUserProfileById, PostHistory } from '@/apis/user';
import { IconLoading } from '@/components/IconLoading';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Spacer } from '@/components/Spacer';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { ListRenderItem } from '@shopify/flash-list';

type Props = {
  profile?: IUserProfileById;
};

export const PostTab = memo(({ profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const userId = profile?.id?.toString();

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: postHistoryData,
    isLoading,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    status,
  } = useGetInfinitePostHistoryQuery({
    userId: userId ?? '',
    limit: 20,
  });

  const postHistory = postHistoryData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<PostHistory>>(
    ({ item }) => <AccountPostItem isEpisode={item.type === 'episode'} postInfo={item} />,
    []
  );

  const renderItemSeparator = useCallback(() => <View style={styles.separator} />, [styles.separator]);

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: PostHistory) => `${item.id}-${item.type}`, []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const isShowEmpty = status === 'success' && postHistory.length === 0;

  const handleRefetch = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(profile?.id ?? '') }),
      queryClient.resetQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() }),
    ]);
  };

  return (
    <TabFlashList
      onRefresh={handleRefetch}
      data={postHistory}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      contentContainerStyle={styles.contentContainer}
      ItemSeparatorComponent={renderItemSeparator}
      ListHeaderComponent={<Spacer height={24} />}
      ListFooterComponent={isFetchingNextPage || isLoading ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='post'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} posted any comments`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {},
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    marginTop: 24,
  },
  skeletonContainer: {
    flexDirection: 'column',
  },
}));
