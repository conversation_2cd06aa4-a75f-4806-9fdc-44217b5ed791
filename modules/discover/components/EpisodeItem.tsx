import { CustomTextWithEndOfLine } from '@/components/CustomTextWithEndOfLine';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { StarAndCommentCount } from './StarAndCommentCount';
import { memo } from 'react';
import { IEpisode } from '@/apis/podcast';
import { format } from 'date-fns';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  episodeItem: IEpisode;
};

const textDateWidth = 42.71571922302246;

export const EpisodeItem = memo(({ episodeItem }: Props) => {
  const { id, podcastTitle, title, imageUrl, releaseDate, rateCount = 0, avgRate = 0 } = episodeItem;
  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handleDirect = () => {
    episodeDetailDirect(queryClient, id.toString());
  };

  const timeFormat = format(new Date(releaseDate), 'MMM dd');

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={[styles.container, styles.itemStart]}>
      <ExpoImage source={{ uri: imageUrl }} style={styles.image} />

      <Spacer width={24} />

      <View style={styles.infoBox}>
        <View style={[styles.podcastInfoContainer, styles.itemCenter]}>
          <CustomTextWithEndOfLine
            text={podcastTitle?.trim()}
            textEndOfLine={timeFormat}
            textEndOfLineWidth={textDateWidth}
            numberOfLines={2}
          />
        </View>

        <Spacer height={4} />

        <ThemedText type='small' numberOfLines={2}>
          {title.trim()}
        </ThemedText>

        <Spacer height={4} />

        <StarAndCommentCount star={Number(avgRate || 0)} rateCount={Number(rateCount || 0)} />
      </View>
    </TouchableOpacity>
  );
});

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 24,
  },
  infoBox: { flex: 1 },
  podcastInfoContainer: {
    flexDirection: 'row',
    // width: '100%',
    // flexWrap: 'wrap',
  },
  itemStart: {
    alignItems: 'flex-start',
  },
  itemCenter: {
    alignItems: 'center',
  },
  image: {
    width: 96,
    height: 96,
    objectFit: 'cover',
    borderRadius: 4,
  },
  podcastTitle: {
    color: theme.colors.neutralWhite,
    opacity: 0.56,
    lineHeight: 18,
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralWhite,
  },
}));
