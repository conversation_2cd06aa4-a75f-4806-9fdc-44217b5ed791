import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useCallback, useEffect, useMemo } from 'react';
import { View } from 'react-native';
import { ActivityItem } from '../ActivityItem';
import { useGetInfiniteMyActivitiesQuery, useGetInfiniteFollowingActivitiesQuery, IActivityLog } from '@/apis/activity';
import { IconLoading } from '@/components/IconLoading';
import { Empty } from '@/components/Empty';
import { useIsFocused } from '@react-navigation/native';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Spacer } from '@/components/Spacer';
import { ListRenderItem } from '@shopify/flash-list';

type ActivityType = 'my' | 'following';

type Props = {
  type?: ActivityType;
};

export const YouActivity = ({ type = 'my' }: Props) => {
  const { styles } = useStyles(stylesheet);
  const isFocused = useIsFocused();
  // Use appropriate query based on type
  const myActivitiesQuery = useGetInfiniteMyActivitiesQuery({ limit: 20 }, { enabled: type === 'my' });
  const followingActivitiesQuery = useGetInfiniteFollowingActivitiesQuery(
    { limit: 20 },
    { enabled: type === 'following' }
  );

  const query = type === 'my' ? myActivitiesQuery : followingActivitiesQuery;

  // Flatten pages data
  const activities = useMemo(() => {
    return query.data?.pages.flatMap((page) => page.data) ?? [];
  }, [query.data]);

  const renderItem = useCallback<ListRenderItem<IActivityLog>>(({ item }) => <ActivityItem activityItem={item} />, []);

  const keyExtractor = useCallback((item: IActivityLog) => item.id.toString(), []);

  const handleLoadMore = useCallback(async () => {
    if (query.hasNextPage && !query.isFetchingNextPage) {
      await query.fetchNextPage();
    }
  }, [query]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isFocused) {
      query.refetch();
    }
  }, [isFocused]);

  const renderGap = useCallback(() => <Spacer height={24} />, []);

  const isShowEmpty = query.isSuccess && activities.length === 0;

  if (query.isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <IconLoading />
      </View>
    );
  }

  return (
    <TabFlashList
      bounces={false}
      data={activities}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      // refreshing={query.isRefetching}
      // onRefresh={query.refetch}
      ListHeaderComponent={renderGap}
      contentContainerStyle={styles.contentContainer}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={query.isFetchingNextPage ? <IconLoading /> : null}
      ItemSeparatorComponent={renderGap}
      ListEmptyComponent={isShowEmpty ? <Empty type='post' emptyText='No activity found' /> : null}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: 24,
  },
  contentContainer: {
    // gap: 24,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  columnWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 11,
  },
  skeletonContainer: {
    gap: 11,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
