import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  text?: string;
  name: string;
  username: string;
  userId: string;
};

export const CommentActivity = ({ type, text, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: userId,
  });

  const isNameTruncate = name?.trim().length > 50;
  const nameParse = isNameTruncate ? name?.trim().slice(0, 50) + '...' : name?.trim();

  const title = useMemo(() => {
    if (type === ActivityTypeEnum.COMMENT) return `${isYou ? 'You' : username} posted comment on `;
    if (type === ActivityTypeEnum.EDIT_COMMENT) return `${isYou ? 'You' : username} edited comment on `;
    if (type === ActivityTypeEnum.DELETE_COMMENT) return `${isYou ? 'You' : username} deleted comment on `;

    return '';
  }, [type, isYou, username]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {title}“
        <ThemedText type='small' style={styles.title}>
          {nameParse}
          {text ? `: ${text}` : ''}
        </ThemedText>
        ”
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
