import { useEffect, useMemo, useState } from 'react';
import { Image, ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { useForgotPassMutation, useVerifyCodeMutation } from '@/apis/auth';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import { REGEX } from '@/constants/regex';
import useOtpCountDown from '@/hooks/useOtpCountDown';
import { toastError } from '@/utils/toast';
import { router } from 'expo-router';
import { useLocalSearchParams } from 'expo-router/build/hooks';
import {
  <PERSON>urs<PERSON>,
  CodeField as RNCodeField,
  useBlurOn<PERSON>ulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';

const CELL_COUNT = 6;

const VerifyCodeForgotPassword = () => {
  const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  const { styles } = useStyles(stylesheet);

  const localParams = useLocalSearchParams();
  const emailOrPhone = (localParams?.emailOrPhone as string)?.trim() ?? '';
  const remainingTime = (localParams?.remainingTime as string)?.trim() ?? '';
  const isValidEmail = REGEX.EMAIL.test(emailOrPhone ?? '');

  const { mutateAsync: verifyCode, isPending: isVerifyingCode } = useVerifyCodeMutation();
  const { mutateAsync: forgotPasswordRequest, isPending: isRequestingVerification } = useForgotPassMutation();

  const { handleCount, timeLeft } = useOtpCountDown();

  const handleContinue = async () => {
    if (value.length !== CELL_COUNT) {
      return;
    }

    try {
      const { resetToken } = await verifyCode({
        code: value,
        identifier: emailOrPhone,
      });

      router.replace({
        pathname: '/reset-password',
        params: {
          token: resetToken,
          emailOrPhone,
        },
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleResend = async () => {
    if (!emailOrPhone) {
      return;
    }

    try {
      await forgotPasswordRequest({
        identifier: emailOrPhone,
      });
      handleCount(Number(remainingTime) || 30);
    } catch (error) {
      toastError(error);
    }
  };

  const handleChangeEmailOrPhone = () => {
    router.back();
  };

  const handleChangeCode = (value: string) => {
    setValue(value.replace(/[^0-9]/g, ''));
  };

  const timeLeftText = useMemo(() => {
    const secondsText = timeLeft < 10 ? `0${timeLeft}` : timeLeft;

    return timeLeft ? ` 00:${secondsText}` : '';
  }, [timeLeft]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (remainingTime) {
      handleCount(Number(remainingTime));
    }
  }, [remainingTime]);

  return (
    <SafeAreaView style={styles.container}>
      <Header isBack />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.title}>Check Your Inbox</ThemedText>

        <View style={styles.emailContainer}>
          <ThemedText style={styles.emailOrText}>
            We have sent an OTP verification to <ThemedText>{`${emailOrPhone}`}</ThemedText>
            <Show when={isValidEmail}>
              <ThemedText>.</ThemedText>
            </Show>
          </ThemedText>

          <TouchableOpacity onPress={handleChangeEmailOrPhone}>
            <ThemedText style={styles.changeEmailOrPhoneText}>
              {isValidEmail ? 'Change Email' : 'Change Phone Number'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <RNCodeField
          ref={ref}
          {...props}
          value={value}
          onChangeText={handleChangeCode}
          cellCount={CELL_COUNT}
          rootStyle={styles.codeFieldRoot}
          keyboardType='number-pad'
          textContentType='oneTimeCode'
          renderCell={({ index, symbol, isFocused }) => (
            <View key={index} style={[styles.cell, isFocused && styles.focusCell]}>
              <ThemedText style={styles.cellText} onLayout={getCellOnLayoutHandler(index)}>
                {symbol || (isFocused ? <Cursor /> : null)}
              </ThemedText>
            </View>
          )}
        />

        <View style={styles.resendContainer}>
          <ThemedText style={styles.resendText}>Didn't receive a code? </ThemedText>
          <TouchableOpacity disabled={timeLeft > 0 || isRequestingVerification} onPress={handleResend}>
            <ThemedText style={[styles.resendLink, timeLeft > 0 && styles.disabledResendLink]}>
              Resend{timeLeftText}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <Button
          type='default'
          style={styles.continueButton}
          disabled={value.length !== CELL_COUNT || isVerifyingCode}
          onPress={handleContinue}
          isLoading={isVerifyingCode}
        >
          Continue
        </Button>
      </ScrollView>

      <View style={styles.bottomBox}>
        {isValidEmail ? (
          <Image source={require('@/assets/images/inbox-email.png')} style={styles.bottomImage} />
        ) : (
          <Image source={require('@/assets/images/sms.png')} style={styles.bottomImage} />
        )}
      </View>
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    marginBottom: 24,
    marginTop: 52,
  },
  emailContainer: {
    alignItems: 'center',
    marginBottom: 48,
    ...theme.fw500,
    fontSize: 14,
  },
  emailOrText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
    marginBottom: 8,
    textAlign: 'center',
  },
  changeEmailOrPhoneText: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  codeFieldRoot: {
    width: '100%',
    marginBottom: 40,
    justifyContent: 'space-between',
  },
  cell: {
    width: 52,
    height: 52,
    borderRadius: 16,
    backgroundColor: theme.colors.neutralCard,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
  },
  focusCell: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  cellText: {
    fontSize: 20,
    lineHeight: 24,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 74,
  },
  resendText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
  },
  resendLink: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  disabledResendLink: {
    opacity: 0.5,
  },
  continueButton: {
    marginHorizontal: 31,
  },
  bottomImage: {
    width: '100%',
    resizeMode: 'cover',
    marginHorizontal: 'auto',
  },
  bottomBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: -1,
    maxHeight: 318,
  },
}));

export default VerifyCodeForgotPassword;
