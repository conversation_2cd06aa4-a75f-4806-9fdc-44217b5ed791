import { useUpdatePasswordMutation, useAddPasswordMutation, useChangePasswordMutation } from '@/apis/auth';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import TextInput from '@/components/ui/TextInput';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { PasswordSchema, PasswordSchemaData } from '@/lib/validations/auth';
import { useUserStore } from '@/store/user';
import queryKeys from '@/utils/queryKeys';
import { toastError, toastSuccess } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { router, useLocalSearchParams } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const CreatePassword = () => {
  const localParams = useLocalSearchParams();
  const identifier = localParams?.identifier as string;
  const resetToken = localParams?.resetToken as string;
  const referralCode = localParams?.referralCode as string;

  const setAccessToken = useUserStore((state) => state.setAccessToken);
  const setRefreshToken = useUserStore((state) => state.setRefreshToken);
  const queryClient = useQueryClient();
  const { styles } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PasswordSchemaData>({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    resolver: zodResolver(PasswordSchema),
  });

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const { mutateAsync: addPassword, isPending: addingPassword } = useAddPasswordMutation();

  const { mutate: changePassword, isPending: changingPassword } = useChangePasswordMutation({
    onSuccess: async (data) => {
      toastSuccess({
        description: 'Change password successfully',
      });

      router.replace({
        pathname: '/(app)/settings/account',
      });
    },
    onError: (error) => {
      toastError(error);
    },
  });

  const { mutate: updatePassword, isPending: updatingPassword } = useUpdatePasswordMutation({
    onSuccess: async (data) => {
      setRefreshToken(data.tokens.refreshToken);
      setAccessToken(data.tokens.accessToken);

      await Promise.all([
        queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
      ]);

      if (data.user.status === 'update_profile') {
        router.replace({
          pathname: '/update-info',
          params: {
            referralCode,
          },
        });
      } else {
        router.replace({ pathname: '/(app)/(tabs)' });
      }
    },
    onError: (error) => {
      toastError(error);
    },
  });

  const onSubmit = async (data: PasswordSchemaData) => {
    if (resetToken) {
      updatePassword({
        newPassword: data.password,
        identifier,
        resetToken,
      });
      return;
    }

    if (userProfile?.hasPassword) {
      changePassword({
        oldPassword: data.password,
        newPassword: data.confirmPassword,
      });
    } else {
      try {
        const isRestricted = onCheckAccountRestricted();
        if (isRestricted) return;

        await addPassword({
          newPassword: data.password,
        });
        router.push({
          pathname: '/(app)/verify-code',
          params: {
            identifierType: JSON.stringify([userProfile?.email || '']),
            isAddPassword: 'true',
            newPassword: data.password,
          },
        });
      } catch (error) {
        toastError(error);
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header isBack />

      <View style={styles.content}>
        <ThemedText style={styles.title}>Set Your Password</ThemedText>

        <View style={styles.formContainer}>
          <Controller
            control={control}
            name='password'
            render={({ field: { onChange, value } }) => (
              <TextInput
                label='Create a Password'
                placeholder='Enter Your Password'
                isPassword
                value={value}
                containerStyle={{
                  marginBottom: 32,
                }}
                onChangeText={onChange}
                error={errors.password?.message}
              />
            )}
          />

          <Controller
            control={control}
            name='confirmPassword'
            render={({ field: { onChange, value } }) => (
              <TextInput
                label='Confirm Your Password'
                placeholder='Re-enter Your Password'
                isPassword
                value={value}
                onChangeText={onChange}
                error={errors.confirmPassword?.message}
              />
            )}
          />
        </View>

        <Button
          onPress={handleSubmit(onSubmit)}
          isLoading={updatingPassword || addingPassword}
          style={styles.button}
          type='default'
        >
          {resetToken ? 'Create Account' : 'Continue'}
        </Button>
      </View>
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
    paddingTop: 52,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    marginBottom: 24,
  },
  formContainer: {
    width: '100%',
    marginBottom: 60,
  },
  button: {
    marginHorizontal: 31,
  },
}));

export default CreatePassword;
