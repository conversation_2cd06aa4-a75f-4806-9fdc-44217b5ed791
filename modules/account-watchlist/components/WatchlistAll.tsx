import { IUserProfileById, UserWatchlistHistory } from '@/apis/user';
import { useGetInfiniteUserWatchlistHistoryQuery } from '@/apis/user/queries';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ExpoImage } from '@/components/ui/Image';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { ListRenderItem, TouchableOpacity } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { useAnimatedScrollHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  tab: string;
  profile?: IUserProfileById;
};

export const WatchlistAll = memo(({ userId, tab, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const [isEndReachedCalled, setIsEndReachedCalled] = useState(false);

  const {
    data: watchlistData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetInfiniteUserWatchlistHistoryQuery({
    userId: userId ?? '',
    limit: 24,
    type: tab !== 'All' ? (tab === 'Shows' ? 'podcast' : 'episode') : undefined,
  });

  const watchlist = watchlistData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserWatchlistHistory) => {
      if (item.type === 'episode') {
        return episodeDetailDirect(queryClient, item.mediaId.toString());
      }

      return showDetailDirect(queryClient, item.mediaId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserWatchlistHistory>>(
    ({ item }) => (
      <TouchableOpacity activeOpacity={0.7} onPress={() => handleDirect(item)}>
        <ExpoImage
          source={{ uri: item.mediaImageUrl }}
          style={{ width: itemSize, height: itemSize, borderRadius: 8 }}
        />
      </TouchableOpacity>
    ),
    [handleDirect]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, isEndReachedCalled, fetchNextPage]);

  const onStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage && !isEndReachedCalled) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, isEndReachedCalled, fetchPreviousPage]);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const keyExtractor = useCallback((item: UserWatchlistHistory) => item.id.toString(), []);

  const handler = useAnimatedScrollHandler({});

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const isShowEmpty = !isFetching && watchlist.length === 0;

  return (
    <Tabs.FlatList
      bounces={false}
      numColumns={3}
      data={watchlist}
      style={styles.container}
      contentContainerStyle={[styles.contentContainer, isShowEmpty ? { justifyContent: 'center' } : {}]}
      columnWrapperStyle={styles.columnWrapper}
      showsVerticalScrollIndicator={false}
      maxToRenderPerBatch={24}
      updateCellsBatchingPeriod={50}
      scrollEventThrottle={16}
      initialNumToRender={24}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onStartReached={onStartReached}
      onStartReachedThreshold={0.4}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      onScroll={handler}
      onMomentumScrollEnd={() => {}}
      onMomentumScrollBegin={handleMomentumScrollBegin}
      ListHeaderComponent={isFetchingPreviousPage ? renderSkeleton : null}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='save'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} ${
              tab === 'All'
                ? 'added any shows or episodes to your watchlist'
                : tab === 'Shows'
                  ? 'added any shows to your watchlist'
                  : 'added any episodes to your watchlist'
            }`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 28,
  },
  columnWrapper: {
    gap: 11,
  },
}));
