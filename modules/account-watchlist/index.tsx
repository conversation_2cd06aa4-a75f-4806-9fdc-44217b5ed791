import { TabBarCollapsibleTabView } from '@/components/TabBarCollapsibleTabView';
import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { WatchlistAll } from './components/WatchlistAll';

export const AccountWatchlist = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header
          title={isPendingUserProfile ? '' : isYou ? 'My Watchlist' : `${userProfileById?.username} Watchlist`}
          isBack
        />
      </View>

      <Tabs.Container
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        initialTabName='All'
        renderTabBar={(TabsProps) => <TabBarCollapsibleTabView {...TabsProps} />}
        revealHeaderOnScroll={false}
      >
        <Tabs.Tab name='All' label={'All'}>
          <WatchlistAll userId={userId} tab={'All'} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name='Shows' label={'Shows'}>
          <WatchlistAll userId={userId} tab={'Shows'} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name='Episodes' label={'Episodes'}>
          <WatchlistAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
