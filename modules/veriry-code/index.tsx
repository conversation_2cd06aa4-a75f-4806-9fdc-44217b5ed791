import { useEffect, useMemo, useState } from 'react';
import { ScrollView, TouchableOpacity, View, Image } from 'react-native';
import { createStyleSheet, mq, useStyles } from 'react-native-unistyles';

import {
  useAddPasswordMutation,
  useChangePasswordMutation,
  useRequestUpdateInforVerificationMutation,
  useRequestVerificationMutation,
  useVerifyCodeForUpdateProfileMutation,
  useVerifyCodeMutation,
  useVerifyPasswordMutation,
} from '@/apis/auth';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import { CODE_LENGTH } from '@/constants/common';
import { REGEX } from '@/constants/regex';
import useOtpCountDown from '@/hooks/useOtpCountDown';
import { toastError, toastSuccess } from '@/utils/toast';
import { router, useLocalSearchParams } from 'expo-router';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as R<PERSON><PERSON><PERSON>ield,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { useGetProfileQuery } from '@/apis/auth/queries';

const VerifyCode = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();

  const queryClient = useQueryClient();
  const identifier = localParams?.identifier as string;
  const identifierTypeParam = localParams?.identifierType as string;
  const remainingTime = localParams?.remainingTime as string;
  const isProfileUpdate = localParams?.isProfileUpdate as string;
  const referralCode = localParams?.referralCode as string;
  const isChangePassword = localParams?.isChangePassword as string;
  const oldPassword = localParams?.oldPassword as string;
  const newPassword = localParams?.newPassword as string;
  const isChangeEmailOrPhone = localParams?.isChangeEmailOrPhone as string;
  const isAddPassword = localParams?.isAddPassword as string;
  const [identifierList, setIdentifierList] = useState<string[]>([]);
  const [currentIdentifierIndex, setCurrentIdentifierIndex] = useState(0);
  const [currentIdentifier, setCurrentIdentifier] = useState('');
  const [currentIdentifierType, setCurrentIdentifierType] = useState('');
  const { data: userProfile } = useGetProfileQuery();

  const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({ value, cellCount: CODE_LENGTH });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  useEffect(() => {
    if (identifierTypeParam) {
      try {
        const parsedIdentifiers = JSON.parse(identifierTypeParam);
        if (Array.isArray(parsedIdentifiers)) {
          setIdentifierList(parsedIdentifiers);
          setCurrentIdentifier(parsedIdentifiers[0]);
          setCurrentIdentifierType(getIdentifierType(parsedIdentifiers[0]));
        } else {
          setIdentifierList([identifierTypeParam]);
          setCurrentIdentifier(identifierTypeParam);
          setCurrentIdentifierType(getIdentifierType(identifierTypeParam));
        }
      } catch {
        const singleIdentifier = identifier || identifierTypeParam;
        setIdentifierList([singleIdentifier]);
        setCurrentIdentifier(singleIdentifier);
        setCurrentIdentifierType(getIdentifierType(singleIdentifier));
      }
    }
  }, [identifierTypeParam, identifier]);

  const getIdentifierType = (identifier: string): string => {
    if (identifier.includes('@')) {
      return 'email';
    }
    return 'phone';
  };

  const isValidEmail = REGEX.EMAIL.test(currentIdentifier ?? '');

  const handleError = (error: any) => {
    toastError(error);
  };

  const { mutate: verifyCode, isPending: isVerifyingCode } = useVerifyCodeMutation({
    onSuccess: (data) => {
      router.replace({
        pathname: '/(app)/create-password',
        params: {
          identifier,
          resetToken: data.resetToken,
          referralCode,
        },
      });
    },
    onError: handleError,
  });

  const { mutate: verifyCodeForUpdateProfile, isPending: isVerifyingCodeForUpdateProfile } =
    useVerifyCodeForUpdateProfileMutation({
      onSuccess: async (data) => {
        if (currentIdentifierIndex < identifierList.length - 1) {
          const nextIndex = currentIdentifierIndex + 1;
          setCurrentIdentifierIndex(nextIndex);
          setCurrentIdentifier(identifierList[nextIndex]);
          requestVerification({
            identifier: identifierList[nextIndex],
          });
          setCurrentIdentifierType(getIdentifierType(identifierList[nextIndex]));
          setValue('');
          handleCount(30);
          toastSuccess({
            description: 'Verification successful',
          });
        } else {
          await Promise.all([
            queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() }),
            queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
            queryClient.refetchQueries({ queryKey: queryKeys.userProfile.identities() }),
          ]);
          if (isChangeEmailOrPhone === 'true' || isAddPassword === 'true' || isChangePassword === 'true') {
            toastSuccess({
              description: 'Edited account successfully',
            });
            router.dismissTo({
              pathname: '/(app)/settings/account',
            });
            return;
          }
          router.back();
        }
      },
      onError: handleError,
    });

  const { mutate: requestChangePassword, isPending: isChangingPassword } = useChangePasswordMutation({
    onSuccess: async (data) => {
      handleCount(data?.remainingTime);
    },
    onError: handleError,
  });

  const { mutate: requestAddPassword, isPending: isRequestingAddPassword } = useAddPasswordMutation({
    onSuccess: (data) => {
      handleCount(data?.remainingTime);
    },
    onError: handleError,
  });

  const { mutate: requestVerificationForSignUp, isPending: isRequestingVerificationForSignUp } =
    useRequestVerificationMutation({
      onSuccess: (data) => {
        handleCount(data?.remainingTime);
      },
      onError: handleError,
    });

  const { mutate: verifyPassword, isPending: isVerifyingPassword } = useVerifyPasswordMutation({
    onSuccess: async (data) => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.identities() }),
      ]);

      toastSuccess({
        description: isAddPassword === 'true' ? 'Add password successfully' : 'Change password successfully',
      });

      router.dismissTo({
        pathname: '/(app)/settings/account',
      });
    },
    onError: handleError,
  });

  const { mutate: requestVerification, isPending: isRequestingVerification } =
    useRequestUpdateInforVerificationMutation({
      onSuccess: (data) => {
        handleCount(data?.remainingTime);
      },
      onError: handleError,
    });
  const { handleCount, timeLeft } = useOtpCountDown();

  const handleContinue = async () => {
    if (value.length === CODE_LENGTH) {
      if (isChangePassword === 'true' || isAddPassword === 'true') {
        verifyPassword({
          code: value,
        });
        return;
      }
      if (isProfileUpdate === 'true' || isChangeEmailOrPhone === 'true') {
        verifyCodeForUpdateProfile({
          code: value,
          identifier: currentIdentifier,
        });
      } else {
        verifyCode({
          code: value,
          identifier: currentIdentifier,
        });
      }
    }
  };

  const handleResend = async () => {
    if (isAddPassword === 'true') {
      requestAddPassword({
        newPassword: newPassword,
      });
      return;
    }
    if (isChangePassword === 'true') {
      requestChangePassword({
        oldPassword: oldPassword,
        newPassword: newPassword,
      });
      return;
    }
    if (isProfileUpdate === 'true' || isChangeEmailOrPhone === 'true') {
      await requestVerification({
        identifier: currentIdentifier,
      });
    } else {
      await requestVerificationForSignUp({
        identifier: currentIdentifier,
      });
    }

    handleCount(Number(remainingTime) || 30);
  };

  const handleChangeIdentifier = () => {
    router.back();
  };

  const handleChangeCode = (value: string) => {
    setValue(value.replace(/[^0-9]/g, ''));
  };

  const timeLeftText = useMemo(() => {
    const secondsText = timeLeft < 10 ? `0${timeLeft}` : timeLeft;

    return timeLeft ? ` 00:${secondsText}` : '';
  }, [timeLeft]);

  const getTitle = () => {
    if (isProfileUpdate === 'true' || isAddPassword === 'true' || isChangePassword === 'true') {
      return 'Check Your Inbox';
    }
    if (isChangeEmailOrPhone === 'true') {
      return isValidEmail ? 'Verify Your Email' : 'Verify Your Mobile Number';
    }
    return 'Enter Your Verification Code';
  };

  const progressText =
    identifierList.length > 1 ? `Step ${currentIdentifierIndex + 1} of ${identifierList.length}` : '';

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (remainingTime) {
      handleCount(Number(remainingTime));
    }
  }, [remainingTime]);

  const useProfileUpdateUI = isProfileUpdate === 'true' || isAddPassword === 'true' || isChangePassword === 'true';

  return (
    <SafeAreaView style={styles.container}>
      <Header isBack />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={useProfileUpdateUI ? styles.scrollContentProfileUpdate : styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.title}>{getTitle()}</ThemedText>

        {/* {progressText && <ThemedText style={styles.progressText}>{progressText}</ThemedText>} */}

        <View style={styles.emailContainer}>
          {useProfileUpdateUI ? (
            <>
              <ThemedText style={styles.emailOrText}>
                We have sent an OTP verification to <ThemedText>{`${currentIdentifier}`}</ThemedText>
                <Show when={isValidEmail}>
                  <ThemedText>.</ThemedText>
                </Show>
              </ThemedText>
              <Show when={isChangePassword !== 'true' && isAddPassword !== 'true'}>
                <TouchableOpacity onPress={handleChangeIdentifier}>
                  <ThemedText style={styles.changeEmailOrPhoneText}>
                    {isValidEmail ? 'Change Email' : 'Change Phone Number'}
                  </ThemedText>
                </TouchableOpacity>
              </Show>
            </>
          ) : (
            <>
              <ThemedText style={styles.emailText}>
                We sent it to <ThemedText>{currentIdentifier}</ThemedText>
              </ThemedText>
              <TouchableOpacity onPress={handleChangeIdentifier}>
                <ThemedText style={styles.changeIdentifierText}>
                  Change {currentIdentifierType === 'email' ? 'Email' : 'Mobile Number'}
                </ThemedText>
              </TouchableOpacity>
            </>
          )}
        </View>

        <RNCodeField
          ref={ref}
          {...props}
          value={value}
          onChangeText={handleChangeCode}
          cellCount={CODE_LENGTH}
          rootStyle={styles.codeFieldRoot}
          keyboardType='number-pad'
          textContentType='oneTimeCode'
          renderCell={({ index, symbol, isFocused }) => (
            <View key={index} style={[styles.cell, isFocused && styles.focusCell]}>
              <ThemedText
                style={useProfileUpdateUI ? styles.cellTextProfileUpdate : styles.cellText}
                onLayout={getCellOnLayoutHandler(index)}
              >
                {symbol || (isFocused ? <Cursor /> : null)}
              </ThemedText>
            </View>
          )}
        />

        <View style={styles.resendContainer}>
          <ThemedText style={styles.resendText}>Didn't receive a code? </ThemedText>
          <TouchableOpacity
            disabled={timeLeft > 0 || (useProfileUpdateUI && isRequestingVerification)}
            onPress={handleResend}
          >
            <ThemedText style={[styles.resendLink, timeLeft > 0 && styles.disabledResendLink]}>
              Resend{timeLeftText}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <Button
          type='default'
          style={useProfileUpdateUI ? styles.continueButtonProfileUpdate : styles.continueButton}
          disabled={value.length !== CODE_LENGTH}
          onPress={handleContinue}
          isLoading={
            isVerifyingCode ||
            isVerifyingCodeForUpdateProfile ||
            isRequestingAddPassword ||
            isChangingPassword ||
            isRequestingVerificationForSignUp ||
            isRequestingVerification ||
            isVerifyingPassword
          }
        >
          Continue
        </Button>
      </ScrollView>

      {useProfileUpdateUI && (
        <View style={styles.bottomBox}>
          {isValidEmail ? (
            <Image source={require('@/assets/images/inbox-email.png')} style={styles.bottomImage} />
          ) : (
            <Image source={require('@/assets/images/sms.png')} style={styles.bottomImage} />
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  scrollContentProfileUpdate: {
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    marginBottom: 24,
    marginTop: 52,
  },
  progressText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: 16,
  },
  emailContainer: {
    alignItems: 'center',
    marginBottom: 48,
    ...theme.fw500,
    fontSize: 14,
  },
  emailText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
    marginBottom: 8,
    textAlign: 'center',
  },
  emailOrText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
    marginBottom: 8,
    textAlign: 'center',
  },
  changeIdentifierText: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  changeEmailOrPhoneText: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  codeFieldRoot: {
    width: '100%',
    marginBottom: 40,
    justifyContent: 'space-between',
    paddingHorizontal: { [mq.only.width(430)]: 0, [mq.only.width('xs')]: 24 },
  },
  cell: {
    width: { [mq.only.width(430)]: 56, [mq.only.width('xs')]: 42 },
    height: { [mq.only.width(430)]: 56, [mq.only.width('xs')]: 42 },
    borderRadius: { [mq.only.width(430)]: 16, [mq.only.width('xs')]: 8 },
    backgroundColor: theme.colors.neutralCard,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
  },
  focusCell: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  cellText: {
    fontSize: { [mq.only.width(430)]: 20, [mq.only.width('xs')]: 16 },
    lineHeight: { [mq.only.width(430)]: 24, [mq.only.width('xs')]: 20 },
    color: theme.colors.neutralWhite,
    textAlign: 'center',
  },
  cellTextProfileUpdate: {
    fontSize: 20,
    lineHeight: 24,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 74,
  },
  resendText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
  },
  resendLink: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  disabledResendLink: {
    opacity: 0.5,
  },
  continueButton: {
    marginHorizontal: 31,
  },
  continueButtonProfileUpdate: {
    marginHorizontal: 31,
  },
  bottomImage: {
    width: '100%',
    resizeMode: 'cover',
    marginHorizontal: 'auto',
  },
  bottomBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: -1,
    maxHeight: 318,
  },
}));

export default VerifyCode;
