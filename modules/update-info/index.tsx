import { useGetProfileQuery } from '@/apis/auth/queries';
import {
  useCheckUserProfileUpdateValidMutation,
  useUpdateUserProfileMutation,
  useUploadImageToS3,
  useUploadUserImageMutation,
} from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import DateTimePickerInput from '@/components/ui/DateTimePicker';
import { Header } from '@/components/ui/Header';
import TextInput from '@/components/ui/TextInput';
import { UpdateUserProfileZodData, UpdateUserProfileZodSchema } from '@/lib/validations/auth';
import { useUserStore } from '@/store/user';
import { convertDateFormat } from '@/utils/dateUtils';
import queryKeys from '@/utils/queryKeys';
import { toastError } from '@/utils/toast';
import { Ionicons } from '@expo/vector-icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { ImagePickerAsset } from 'expo-image-picker';
import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Alert, Image, Platform, ScrollView, TouchableOpacity, View } from 'react-native';
import { AdvancedCheckbox } from 'react-native-advanced-checkbox';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const defaultAvatar = require('@/assets/images/default-avatar.png');

const UpdateInfo = () => {
  const localParams = useLocalSearchParams();
  const referralCode = localParams?.referralCode as string;

  const { theme, styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const signOut = useUserStore.use.signOut();

  const [profileImage, setProfileImage] = useState<ImagePickerAsset>();
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const { data: userProfile } = useGetProfileQuery();
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<UpdateUserProfileZodData>({
    defaultValues: {
      username: '',
      email: userProfile?.email || '',
      dateOfBirth: '',
      referral: referralCode,
    },
    resolver: zodResolver(UpdateUserProfileZodSchema),
  });
  const { mutateAsync: updateUserProfile, isPending: isUpdatingUserProfile } = useUpdateUserProfileMutation();
  const { mutateAsync: uploadUserImage, isPending: isUploadingUser } = useUploadUserImageMutation();
  const { mutateAsync: uploadImageToS3, isPending: isUploadingToS3 } = useUploadImageToS3();
  const { mutateAsync: checkUserProfileUpdateValid, isPending: isCheckingUserProfileUpdateValid } =
    useCheckUserProfileUpdateValidMutation();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (userProfile?.email) {
      setValue('email', userProfile?.email);
    }
  }, [userProfile]);

  const onSubmit = async (data: UpdateUserProfileZodData) => {
    try {
      let profileImageUrl: string = '';

      if (profileImage) {
        const imagePath = profileImage.uri;

        const uploadUserImageRes = await uploadUserImage({
          file: profileImage,
        });

        if (uploadUserImageRes?.presigned) {
          await uploadImageToS3({
            file: profileImage.uri,
            url: uploadUserImageRes?.presigned,
          });
        }

        profileImageUrl = uploadUserImageRes?.url;
      }

      if (userProfile?.id) {
        if (!userProfile?.email) {
          // Check data valid
          const { remainingTime } = await checkUserProfileUpdateValid({
            username: data.username,
            email: data.email,
            referral: data?.referral,
            dateOfBirth: convertDateFormat(data.dateOfBirth),
          });

          router.push({
            pathname: '/verify-email-profile',
            params: {
              username: data.username,
              email: data.email,
              dateOfBirth: convertDateFormat(data.dateOfBirth),
              referral: data?.referral,
              avatar: profileImageUrl,
              userId: userProfile?.id,
              remainingTime: remainingTime ?? 30,
            },
          });
        } else {
          await updateUserProfile({
            username: data.username,
            email: data.email,
            dateOfBirth: convertDateFormat(data.dateOfBirth),
            referral: data?.referral,
            avatar: profileImageUrl,
            userId: userProfile?.id,
          });

          await Promise.all([
            queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
            queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
          ]);

          router.replace({
            pathname: '/choose-plan',
          });
        }
      }
    } catch (error) {
      toastError(error);
    }
  };

  const isLoadingSubmit = useMemo(
    () => isUpdatingUserProfile || isUploadingUser || isUploadingToS3 || isCheckingUserProfileUpdateValid,
    [isUpdatingUserProfile, isUploadingUser, isUploadingToS3, isCheckingUserProfileUpdateValid]
  );

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission denied', 'We need camera permissions to take a photo');
      return;
    }

    setIsUploadingImage(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    } finally {
      setIsUploadingImage(false);
    }
  };

  const openGallery = async () => {
    setIsUploadingImage(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0]);
      }
    } catch (error) {
      const hasErrorCode = (err: unknown): err is { code: string; message?: string } => {
        return typeof err === 'object' && err !== null && 'code' in err;
      };

      if (hasErrorCode(error)) {
        if (error.code === 'ERR_FAILED_TO_READ_IMAGE') {
          toastError('Image is invalid. Please try again');
        }
      }
    } finally {
      setIsUploadingImage(false);
    }
  };

  const pickImage = async () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose an option',
      [
        {
          text: 'Take Photo',
          onPress: openCamera,
        },
        {
          text: 'Choose from Gallery',
          onPress: openGallery,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ],
      { cancelable: true }
    );
  };

  const handleGoBack = () => {
    if (router.canDismiss()) {
      router.dismissAll();
    }
    queryClient.removeQueries();
    signOut();
    router.replace('/sign-in');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.pContainer}>
        <Header isBack onBackPress={handleGoBack} />
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <ThemedText style={styles.title}>Your Details</ThemedText>

          <View style={styles.profileContainer}>
            <View style={styles.profileImageContainer}>
              {isUploadingImage ? (
                <View style={styles.profileImage}>
                  <ThemedText>Loading...</ThemedText>
                </View>
              ) : (
                <Image source={profileImage ? { uri: profileImage.uri } : defaultAvatar} style={styles.profileImage} />
              )}
              <TouchableOpacity style={styles.editButton} onPress={pickImage} disabled={isUploadingImage}>
                <Icons.EditV2 size={16} color={theme.colors.background} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formContainer}>
            <Controller
              control={control}
              name='username'
              rules={{
                required: 'Username is required',
              }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label='Username'
                  value={value}
                  onChangeText={(value) => onChange(value.trim())}
                  error={errors.username?.message}
                  containerStyle={styles.inputSpacing}
                  placeholder='Please Enter Your Name'
                />
              )}
            />

            <Controller
              control={control}
              name='email'
              rules={{
                required: 'Email is required',
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'Invalid email address',
                },
              }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label='Email'
                  value={value}
                  onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                  autoCapitalize='none'
                  editable={!userProfile?.email}
                  keyboardType='email-address'
                  error={errors.email?.message}
                  containerStyle={styles.inputSpacing}
                  placeholder='Input Your Email'
                />
              )}
            />

            <Controller
              control={control}
              name='dateOfBirth'
              rules={{
                required: 'Date of Birth is required',
              }}
              render={({ field: { onChange, value } }) => (
                <DateTimePickerInput
                  label='Date of Birth'
                  value={value}
                  onChange={onChange}
                  error={errors.dateOfBirth?.message}
                  containerStyle={styles.inputSpacing}
                  placeholder='MM/DD/YYYY'
                />
              )}
            />

            <Controller
              control={control}
              name='referral'
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label='Referral Code (Optional)'
                  value={value}
                  onChangeText={onChange}
                  leftIcon={<Ionicons name='gift-outline' size={20} color='#FFFFFF' />}
                  containerStyle={styles.inputSpacing}
                  placeholder='Please Enter Your Referral Code'
                />
              )}
            />

            <View style={styles.termsContainer}>
              <AdvancedCheckbox
                size={22}
                value={termsAccepted}
                onValueChange={(value) => setTermsAccepted(!!value)}
                checkedColor={theme.colors.primary}
                uncheckedColor='transparent'
                containerStyle={styles.termCheckboxContainer}
                checkBoxStyle={styles.termCheckboxInner}
                checkMarkContent={<Ionicons name='checkmark' size={16} color='black' />}
              />
              <ThemedText style={styles.termsText}>
                By signing up, I agree to the <ThemedText style={styles.termsLink}>Terms & Conditions</ThemedText>
              </ThemedText>
            </View>
          </View>

          <Button
            type='default'
            onPress={handleSubmit(onSubmit)}
            disabled={!termsAccepted}
            style={styles.button}
            isLoading={isLoadingSubmit}
          >
            Update Profile
          </Button>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    marginVertical: 16,
    textAlign: 'center',
  },
  profileContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  profileImageContainer: {
    position: 'relative',
    width: 112,
    height: 112,
  },
  profileImage: {
    width: 112,
    height: 112,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 13,
    right: -6,
    width: 30,
    height: 30,
    borderRadius: 999,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: '100%',
    marginBottom: 24,
  },
  inputSpacing: {
    marginBottom: 24,
  },
  button: {
    marginTop: 16,
    marginHorizontal: 31,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  termsText: {
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.neutralWhite,
  },
  termsLink: {
    color: theme.colors.primary,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  termCheckbox: {
    backgroundColor: 'black',
  },
  termCheckboxContainer: {
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    borderRadius: 8,
    color: theme.colors.neutralBackground,
  },
  termCheckboxInner: {
    borderRadius: 8,
  },
  pContainer: {
    paddingHorizontal: 24,
  },
}));

export default UpdateInfo;
