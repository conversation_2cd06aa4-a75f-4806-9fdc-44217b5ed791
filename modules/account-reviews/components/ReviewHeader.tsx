import { useGetUserRateStatsReviewQuery } from '@/apis/user/queries';
import { RateStar } from '@/components/RateStar';
import { ThemedText } from '@/components/ThemedText';
import BarGraph from '@/components/BarGraph';
import { ratingDistribution } from '@/utils/const';
import { formatCompactNumber } from '@/utils/func';
import { Skeleton } from 'moti/skeleton';
import { useMemo, useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TReviewTab } from '..';

type Props = {
  userId: string;
  currentTab: TReviewTab;
};

export const ReviewHeader = ({ userId, currentTab }: Props) => {
  const { styles } = useStyles(stylesheet);
  const [focusingRate, setFocusingRate] = useState<string>();

  const { data: userRateStats, isPending: isLoadingStats } = useGetUserRateStatsReviewQuery({
    userId,
    type: currentTab !== 'All' ? (currentTab === 'Show' ? 'podcast' : 'episode') : undefined,
  });
  const userAvgRateStating = Number(userRateStats?.totalRatings || 0);
  const avgRate = Number(focusingRate) || Number(userRateStats?.averageRating || 0);

  const formattedDistribution = ratingDistribution.map((item) => {
    const distributionItem = userRateStats?.distribution.find((dist) => dist.rateValue.toString() === item.label);

    return {
      ...item,
      value: distributionItem ? +distributionItem.count : item.value,
    };
  });

  const focusingRateCount = formattedDistribution.find((item) => item.label === focusingRate)?.value || 0;

  const ratingCountText = useMemo(() => {
    if (focusingRate) {
      return `${formatCompactNumber(Number(focusingRateCount || 0))} ${focusingRateCount <= 1 ? 'Rating' : 'Ratings'}`;
    }

    return `${formatCompactNumber(userAvgRateStating)} ${userAvgRateStating <= 1 ? 'Rating' : 'Ratings'}`;
  }, [userAvgRateStating, focusingRate, focusingRateCount]);

  return (
    <View style={styles.container}>
      <View style={styles.ratingCard}>
        <View style={styles.ratingHeader}>
          <ThemedText style={styles.ratingValue}>{Number(avgRate).toFixed(1)}</ThemedText>

          <RateStar rating={avgRate} size={28} gap={12} />
        </View>

        {isLoadingStats ? (
          <View style={styles.ratingsCount}>
            <Skeleton width={'100%'} height={18} />
          </View>
        ) : (
          <ThemedText style={styles.ratingsCount}>{ratingCountText}</ThemedText>
        )}

        <View style={styles.chartContainer}>
          {isLoadingStats ? (
            <Skeleton width={'100%'} height={104} />
          ) : (
            <BarGraph onChange={setFocusingRate} data={formattedDistribution} />
          )}
        </View>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  ratingCard: {
    marginTop: 28,
    padding: 16,
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 12,
    minHeight: 215,
  },
  ratingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 24,
    ...theme.fw700,
    marginRight: 12,
    lineHeight: 29,
    color: theme.colors.neutralWhite,
  },
  ratingsCount: {
    marginTop: 8,
    color: theme.colors.neutralWhite,
  },
  chartContainer: {
    marginTop: 20,
  },
  reviewsSection: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 24,
    ...theme.fw700,
    marginBottom: 16,
    color: theme.colors.neutralWhite,
  },
}));
