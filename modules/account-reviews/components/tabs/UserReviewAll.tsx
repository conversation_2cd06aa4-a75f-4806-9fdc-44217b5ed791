import { IUserProfileById, UserRateReview } from '@/apis/user';
import { useGetInfiniteUserRatesReviewQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { getItemSizeFlatList } from '@/utils/func';
import { memo, useCallback, useState } from 'react';
import { ListRenderItem } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { useAnimatedScrollHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TReviewTab } from '../..';
import { UserReviewItem } from '../UserReviewItem';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  tab: TReviewTab;
  profile?: IUserProfileById;
};

export const UserReviewAll = memo(({ userId, tab, profile }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const [isEndReachedCalled, setIsEndReachedCalled] = useState(false);

  const currentType = tab !== 'All' ? (tab === 'Show' ? 'podcast' : 'episode') : undefined;

  const {
    data: userRatesReviewData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetInfiniteUserRatesReviewQuery({
    userId: userId ?? '',
    limit: 24,
    type: currentType,
  });

  const userRatesReview = userRatesReviewData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<UserRateReview>>(
    ({ item }) => <UserReviewItem userRateReview={item} />,
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, isEndReachedCalled, fetchNextPage]);

  const onStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage && !isEndReachedCalled) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, isEndReachedCalled, fetchPreviousPage]);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const keyExtractor = useCallback((item: UserRateReview) => item.id.toString(), []);

  const handler = useAnimatedScrollHandler({});

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const isShowEmpty = !isFetching && userRatesReview.length === 0;

  return (
    <Tabs.FlatList
      bounces={false}
      data={userRatesReview}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      maxToRenderPerBatch={24}
      updateCellsBatchingPeriod={50}
      scrollEventThrottle={16}
      initialNumToRender={24}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onStartReached={onStartReached}
      onStartReachedThreshold={0.4}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      onScroll={handler}
      onMomentumScrollEnd={() => {}}
      onMomentumScrollBegin={handleMomentumScrollBegin}
      ListHeaderComponent={isFetchingPreviousPage ? renderSkeleton : null}
      ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='star'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} ${tab === 'All' ? 'rated any shows or episodes' : tab === 'Show' ? 'rated any shows' : 'rated any episodes'}`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 28,
  },
  columnWrapper: {
    gap: 11,
  },
}));
