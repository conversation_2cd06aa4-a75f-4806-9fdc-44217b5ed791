import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { TabBarCollapsibleTabView } from '@/components/TabBarCollapsibleTabView';
import { Header } from '@/components/ui/Header';
import { useIsYou } from '@/hooks/useIsYou';
import { useLocalSearchParams } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { UserReviewAll } from './components/tabs/UserReviewAll';
import { useCallback, useState } from 'react';
import { ReviewHeader } from './components/ReviewHeader';
import { IndexChangeEventData } from 'react-native-collapsible-tab-view/lib/typescript/src/types';
import { Tabs } from 'react-native-collapsible-tab-view';

type Props = {};

export type TReviewTab = 'All' | 'Episodes' | 'Show';

export const AccountReviews = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();
  const [selectedTab, setSelectedTab] = useState<TReviewTab>('All');

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  const handleTabChange = ({ tabName }: IndexChangeEventData<TReviewTab>) => {
    setSelectedTab(tabName);
  };

  const renderHeader = useCallback(() => {
    return <ReviewHeader userId={userId} currentTab={selectedTab} />;
  }, [userId, selectedTab]);

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header
          title={isPendingUserProfile ? '' : isYou ? 'My Reviews' : `${userProfileById?.username} Reviews`}
          isBack
        />
      </View>

      <Tabs.Container
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        initialTabName={'All'}
        renderHeader={renderHeader}
        onTabChange={handleTabChange as any}
        renderTabBar={(TabsProps) => <TabBarCollapsibleTabView {...TabsProps} />}
        revealHeaderOnScroll={false}
      >
        <Tabs.Tab name={'All'} label={'All'}>
          <UserReviewAll userId={userId} tab={'All'} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name={'Show'} label={'Show'}>
          <UserReviewAll userId={userId} tab={'Show'} profile={userProfileById} />
        </Tabs.Tab>

        <Tabs.Tab name={'Episodes'} label={'Episodes'}>
          <UserReviewAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
