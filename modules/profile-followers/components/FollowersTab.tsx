import { useCallback, useMemo, useState, useEffect } from 'react';
import { ListRenderItem, View } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { UserListItem } from '@/components/ui/UserListItem';
import { Spacer } from '@/components/Spacer';
import { useGetFollowersInfiniteQuery, type IFollow } from '@/apis/follow';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

interface FollowersTabProps {
  onFollowToggle: (userId: string, isFollowed: boolean) => void;
  searchQuery: string;
  targetUserId?: string;
  isOwner: boolean;
}

export const FollowersTab = ({ onFollowToggle, searchQuery, targetUserId, isOwner }: FollowersTabProps) => {
  const { styles } = useStyles(stylesheet);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const { data: userProfile } = useGetProfileQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const {
    data: followersData,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetFollowersInfiniteQuery({
    targetUserId: Number(targetUserId),
    limit: 20,
    search: debouncedSearchQuery,
  });

  const allFollowers = useMemo(() => {
    const infiniteData = followersData as any;
    if (!infiniteData?.pages) return [];

    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [followersData]);

  const handleFollowToggle = useCallback(
    (userId: string, isFollowed: boolean) => {
      onFollowToggle(userId, isFollowed);
    },
    [onFollowToggle]
  );

  const renderUserItem = useCallback<ListRenderItem<IFollow>>(
    ({ item }) => {
      return (
        <UserListItem
          user={item}
          onUserPress={() => {
            const isRestricted = onCheckAccountRestricted();
            if (isRestricted) return;

            router.push({
              pathname: '/(app)/[userId]',
              params: {
                userId: item?.id?.toString(),
              },
            });
          }}
          onFollowToggle={handleFollowToggle}
          showFollowButton={item.id !== userProfile?.id}
        />
      );
    },
    [handleFollowToggle, onCheckAccountRestricted, userProfile?.id]
  );

  const keyExtractor = useCallback((item: IFollow) => item.id?.toString() || '', []);

  const renderSeparator = useCallback(() => <Spacer height={24} />, []);

  const listHeaderComponent = useMemo(
    () => (
      <View>
        <Spacer height={16} />
      </View>
    ),
    []
  );

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (isLoading && !followersData) {
    return (
      <Tabs.FlatList
        bounces={false}
        data={[]}
        renderItem={() => null}
        contentContainerStyle={[styles.listContainer, { justifyContent: 'center' }]}
        ListHeaderComponent={<Spacer height={200} />}
      />
    );
  }

  if (error) {
    return (
      <Tabs.FlatList
        bounces={false}
        data={[]}
        renderItem={() => null}
        contentContainerStyle={[styles.listContainer, { justifyContent: 'center' }]}
        ListHeaderComponent={<Spacer height={200} />}
        refreshing={isLoading}
        onRefresh={refetch}
      />
    );
  }

  return (
    <Tabs.FlatList
      bounces={false}
      data={allFollowers}
      renderItem={renderUserItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={renderSeparator}
      ListHeaderComponent={listHeaderComponent}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      refreshing={isLoading}
      onRefresh={refetch}
    />
  );
};

const stylesheet = createStyleSheet(() => ({
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: 100,
    minHeight: 100,
  },
}));
