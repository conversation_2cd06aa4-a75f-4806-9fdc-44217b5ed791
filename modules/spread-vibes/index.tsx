import { useGetProfileQuery } from '@/apis/auth/queries';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { Image, ScrollView, Share, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import * as Clipboard from 'expo-clipboard';
import { useGetRefLink } from '@/hooks/useGetRefLink';
import { toastSuccess } from '@/utils/toast';

const SpreadVibes = () => {
  const { styles } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const { refLink, refCode } = useGetRefLink();

  const handleCopyReferral = async () => {
    toastSuccess({ description: 'Copied!' });
    await Clipboard.setStringAsync(refCode);
  };

  const handleOpenShare = () => {
    Share.share({
      message: `Join me on Rabid to discuss, rate, and track your podcast activity!
      👉 Try it here: ${refLink}`,
    });
  };

  return (
    <View style={styles.container}>
      <Header isBack title='Spread the Vibes' />

      <ScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false} bounces={false}>
        <Image source={require('@/assets/images/spread-vibes.png')} style={styles.image} />

        <Spacer height={40} />

        <ThemedText style={styles.centerText} type='subtitle'>
          Share Rabid with Friends
        </ThemedText>

        <Spacer height={16} />

        <ThemedText style={styles.centerText} type='small'>
          Invite your friends and get inspired, track your favorite podcasts and build the community together.
        </ThemedText>

        <Spacer height={28} />

        <View style={styles.referralBox}>
          <View style={styles.fullFlex}>
            <ThemedText type='tinyMedium'>Referral Code</ThemedText>

            <Spacer height={4} />

            <ThemedText type='defaultBold'>{userProfile?.referral}</ThemedText>
          </View>

          <TouchableOpacity activeOpacity={0.7} style={styles.rowHCenter} onPress={handleCopyReferral}>
            <Icons.Copy size={24} />

            <Spacer width={8} />

            <ThemedText type='smallSemiBold' style={styles.textPrimary}>
              Copy
            </ThemedText>
          </TouchableOpacity>
        </View>

        <Spacer height={40} />

        <CustomButton textType='defaultBold' onPress={handleOpenShare}>
          Invite Friends
        </CustomButton>
      </ScrollView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
    paddingHorizontal: 24,
  },
  contentContainer: {
    paddingVertical: 32,
  },
  image: {
    width: '100%',
    height: undefined,
    resizeMode: 'contain',
    aspectRatio: 1,
  },
  centerText: {
    textAlign: 'center',
  },
  referralBox: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
  },
  rowHCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  fullFlex: {
    flex: 1,
  },
}));

export default SpreadVibes;
