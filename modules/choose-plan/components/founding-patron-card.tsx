import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Benefit } from '@/modules/subscription-plan/components/Benefit';
import { useMemo } from 'react';
import { Image, View } from 'react-native';
import { PurchasesPackage } from 'react-native-purchases';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  packageItem: PurchasesPackage;
};

const FoundingPatronCard = ({ packageItem }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { product } = packageItem;
  const { subscriptionPeriod, priceString } = product;

  const subscriptionPeriodValue = useMemo(() => {
    if (subscriptionPeriod === 'P1W') return '/weekly, billed weekly';
    if (subscriptionPeriod === 'P1M') return '/month, billed monthly';
    if (subscriptionPeriod === 'P1Y') return '/year, billed annually';

    return '';
  }, [subscriptionPeriod]);

  return (
    <View style={styles.planCard}>
      <ThemedText style={styles.planTitle}>Founding Patron</ThemedText>

      <View style={styles.priceContainer}>
        <ThemedText style={styles.priceAmount}>{priceString}</ThemedText>

        <ThemedText style={styles.pricePeriod}>{subscriptionPeriodValue}</ThemedText>
      </View>

      <ThemedText style={styles.planDescription}>
        Support the team and help us build even more amazing features! By subscribing, you directly contribute to future
        updates, and get benefit:
      </ThemedText>

      <Benefit icon={<Icons.RabidCoin size={24} color='#D9FF03' />} title="Commemorative Rabid's Metal Coin" />

      <Spacer height={12} />

      <Benefit
        icon={<Image source={require('@/assets/images/profile-icon.png')} style={{ width: 24, height: 24 }} />}
        title='Patron Special Profile'
      />

      <Spacer height={12} />

      <Benefit icon={<Icons.EditV2 size={24} color='#D9FF03' />} title='Edit Comment/Reply' />

      <Spacer height={12} />

      <Benefit icon={<Icons.Verify size={24} color='#D9FF03' />} title='Verified Profile' />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  planCard: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
  },
  planTitle: {
    fontSize: 20,
    lineHeight: 28,
    ...theme.fw500,
    marginBottom: 24,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    height: 32,
  },
  priceAmount: {
    fontSize: 28,
    ...theme.fw700,
    lineHeight: 32,
  },
  pricePeriod: {
    fontSize: 14,
    marginLeft: 4,
    opacity: 0.8,
  },
  planDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 29,
    opacity: 0.9,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  benefitItemMarginBottom: {
    marginBottom: 12,
  },
  checkIcon: {
    width: 24,
    height: 24,
    marginRight: 16,
  },
  benefitText: {
    fontSize: 14,
    opacity: 0.9,
  },
}));

export default FoundingPatronCard;
