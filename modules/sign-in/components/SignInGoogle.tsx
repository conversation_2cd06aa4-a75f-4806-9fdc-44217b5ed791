import { useLoginMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { toastError } from '@/utils/toast';
import {
  GoogleSignin,
  isErrorWithCode,
  isSuccessResponse,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { router } from 'expo-router';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import { useSignInContext } from '@/modules/account-settings/components/SignInMethodsSection';
import { useEffect, useState } from 'react';
import { env } from '@/utils/const';
import AccountUpdatedModal from './AccountUpdatedModal';

GoogleSignin.configure({
  webClientId: env.WEB_ID, // client ID of type WEB for your server. Required to get the `idToken` on the user object, and for offline access.
  scopes: ['profile', 'email'], // what API you want to access on behalf of the user, default is email and profile
  offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
  hostedDomain: '', // specifies a hosted domain restriction
  forceCodeForRefreshToken: false, // [Android] related to `serverAuthCode`, read the docs link below *.
  accountName: '', // [Android] specifies an account name on the device that should be used
  iosClientId: env.IOS_ID, // [iOS] if you want to specify the client ID of type iOS (otherwise, it is taken from GoogleService-Info.plist)
  googleServicePlistPath: '', // [iOS] if you renamed your GoogleService-Info file, new name here, e.g. "GoogleService-Info-Staging"
  openIdRealm: '', // [iOS] The OpenID2 realm of the home web server. This allows Google to include the user's OpenID Identifier in the OpenID Connect ID token.
  profileImageSize: 120, // [iOS] The desired height (and width) of the profile image. Defaults to 120px
});

type Props = {
  isOnlyIcon?: boolean;
  onAccountUpdated?: () => void;
};

export const SignInGoogle = ({ isOnlyIcon, onAccountUpdated }: Props) => {
  const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
  const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
  const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();
  const { isAddMethodMode, isChangeMethodMode, currentProviderType, onMethodAdded, onMethodChanged } =
    useSignInContext();

  const setAccessToken = useUserStore.use.setAccessToken();
  const setRefreshToken = useUserStore.use.setRefreshToken();
  const { styles } = useStyles(stylesheet);

  const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
  const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');

  useEffect(() => {
    if (isChangeMethodMode && currentProviderType === 'google') {
      GoogleSignin.signOut()
        .then(() => {
          handleSignInGoogle();
        })
        .catch(() => {
          handleSignInGoogle();
        });
    }
  }, [isChangeMethodMode, currentProviderType]);

  const handleSignInGoogle = async () => {
    let idTokenFinal = '';
    try {
      await GoogleSignin.hasPlayServices();

      const signInOptions = isChangeMethodMode
        ? {
            loginHint: '',
            forceCodeForRefreshToken: true,
          }
        : undefined;

      const response = await GoogleSignin.signIn(signInOptions);
      if (isSuccessResponse(response)) {
        const {
          data: { idToken },
        } = response;
        if (!idToken) {
          return toastError('Authentication denied');
        }
        idTokenFinal = idToken;

        GoogleSignin.signOut();

        if (isAddMethodMode) {
          await addMethodMutation({
            provider: 'google',
            token: idToken,
          });
          onMethodAdded?.();
        } else if (isChangeMethodMode) {
          await updateMethodMutation({
            provider: 'google',
            token: idToken,
          });
          onMethodChanged?.();
        } else {
          const loginResult = await loginMutation({
            provider: 'google',
            token: idToken,
          });

          const { user, tokens } = loginResult;
          setRefreshToken(tokens.refreshToken);
          setAccessToken(tokens.accessToken);

          if (router.canDismiss()) router.dismissAll();
          if (user.status === 'complete') {
            router.replace({ pathname: '/(app)/(tabs)' });
          } else if (user.status === 'update_profile') {
            router.replace({ pathname: '/update-info' });
          } else if (user.status === 'plan_payment') {
            router.replace({ pathname: '/choose-plan' });
          } else if (user.status === 'choose_interest') {
            router.replace({ pathname: '/choose-interest' });
          } else if (user.status === 'choose_podcast') {
            router.replace({ pathname: '/choose-podcast' });
          }
        }
      } else {
        toastError('Authentication denied');
        if (isChangeMethodMode || isAddMethodMode) {
          return;
        }
        // if (router.canDismiss()) router.dismissAll();
        // router.replace('/(app)/(tabs)');
      }
    } catch (error: any) {
      if (error.message == 'Your email account has been updated') {
        if (idTokenFinal) {
          setAccountUpdatedToken(idTokenFinal);
          setIsAccountUpdatedModalVisible(true);
        }
        return;
      }
      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            toastError('Sign in already in progress');
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            toastError('Play services not available or outdated');
            break;
          default:
            toastError(error?.message ?? 'Something went wrong');
        }
      } else {
        toastError('Unable to connect to your account');
      }
    }
  };

  return (
    <>
      <SignInSocialButton
        disabled={isPendingLogin || isPendingAddMethod || isPendingUpdateMethod}
        onPress={handleSignInGoogle}
        Icon={<Icons.Google size={24} />}
        title='Google'
        containerStyle={styles.container}
        isOnlyIcon={isOnlyIcon}
      />
      <AccountUpdatedModal
        isVisible={isAccountUpdatedModalVisible}
        onClose={() => setIsAccountUpdatedModalVisible(false)}
        token={accountUpdatedToken}
        provider='google'
      />
    </>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralWhite,
  },
}));
