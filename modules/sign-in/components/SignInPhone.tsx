import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import { useSignInContext } from '@/modules/account-settings/components/SignInMethodsSection';

type Props = { isOnlyIcon?: boolean };

export const SignInPhone = ({ isOnlyIcon }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { isAddMethodMode, isChangeMethodMode } = useSignInContext();

  const handleSignInWithPhone = () => {
    if (isAddMethodMode || isChangeMethodMode) {
      router.replace({
        pathname: '/(app)/settings/account',
      });
    } else {
      router.replace('/sign-in-with-phone');
    }
  };

  return (
    <SignInSocialButton
      onPress={handleSignInWithPhone}
      Icon={<Icons.Phone size={24} />}
      title='Phone Number'
      containerStyle={styles.container}
      textStyle={styles.text}
      isOnlyIcon={isOnlyIcon}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.whiteOpacity24,
  },
  text: {
    color: theme.colors.neutralWhite,
  },
}));
