import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import { useSignInContext } from '@/modules/account-settings/components/SignInMethodsSection';

type Props = { isOnlyIcon?: boolean };

export const SignInEmail = ({ isOnlyIcon }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { isAddMethodMode, isChangeMethodMode } = useSignInContext();

  const handleSignInWithEmail = () => {
    if (isAddMethodMode || isChangeMethodMode) {
      router.replace({
        pathname: '/(app)/settings/account',
      });
    } else {
      router.replace('/sign-in-with-email');
    }
  };

  return (
    <SignInSocialButton
      onPress={handleSignInWithEmail}
      Icon={<Icons.Email size={24} />}
      title='Email'
      containerStyle={styles.container}
      textStyle={styles.text}
      isOnlyIcon={isOnlyIcon}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.whiteOpacity24,
  },
  text: {
    color: theme.colors.neutralWhite,
  },
}));
