import { useLoginMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { toastError } from '@/utils/toast';
import appleAuth, { appleAuthAndroid } from '@invertase/react-native-apple-authentication';
import { router } from 'expo-router';
import { Platform } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';
import { SignInSocialButton } from './SignInSocialButton';
import { useSignInContext } from '@/modules/account-settings/components/SignInMethodsSection';
import { useEffect, useState } from 'react';
import AccountUpdatedModal from './AccountUpdatedModal';

type Props = {
  isOnlyIcon?: boolean;
};

export const SignInApple = ({ isOnlyIcon }: Props) => {
  const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
  const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
  const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();
  const { isAddMethodMode, isChangeMethodMode, currentProviderType, onMethodAdded, onMethodChanged } =
    useSignInContext();

  const setAccessToken = useUserStore.use.setAccessToken();
  const setRefreshToken = useUserStore.use.setRefreshToken();
  const { styles } = useStyles(stylesheet);

  const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
  const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');

  useEffect(() => {
    if (isChangeMethodMode && currentProviderType === 'apple') {
      if (Platform.OS === 'android') {
        handleAppleSignInOnAndroid();
      } else {
        handleAppleSignInOnIOS();
      }
    }
  }, [isChangeMethodMode, currentProviderType]);

  const signIn = async (idToken: string) => {
    if (isAddMethodMode) {
      await addMethodMutation({
        provider: 'apple',
        token: idToken,
      });
      onMethodAdded?.();
    } else if (isChangeMethodMode) {
      await updateMethodMutation({
        provider: 'apple',
        token: idToken,
      });
      onMethodChanged?.();
    } else {
      try {
        const loginResult = await loginMutation({
          provider: 'apple',
          token: idToken,
        });
        const { tokens, user } = loginResult;
        setRefreshToken(tokens.refreshToken);
        setAccessToken(tokens.accessToken);
        if (router.canDismiss()) router.dismissAll();

        if (user.status === 'complete') {
          router.replace({ pathname: '/(app)/(tabs)' });
        } else if (user.status === 'update_profile') {
          router.replace({ pathname: '/update-info' });
        } else if (user.status === 'plan_payment') {
          router.replace({ pathname: '/choose-plan' });
        } else if (user.status === 'choose_interest') {
          router.replace({ pathname: '/choose-interest' });
        } else if (user.status === 'choose_podcast') {
          router.replace({ pathname: '/choose-podcast' });
        }
      } catch (error: any) {
        if (error.message == 'Your email account has been updated') {
          setAccountUpdatedToken(idToken);
          setIsAccountUpdatedModalVisible(true);
          return;
        }
        toastError(error);
      }
    }
  };

  const handleAppleSignInOnIOS = async () => {
    try {
      const requestOptions = {
        requestedOperation: appleAuth.Operation.LOGIN,
        // Note: it appears putting FULL_NAME first is important, see issue #293
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      };

      // Force fresh sign-in when in change mode
      if (isChangeMethodMode) {
        requestOptions.requestedOperation = appleAuth.Operation.REFRESH;
      }

      const appleAuthRequestResponse = await appleAuth.performRequest(requestOptions);

      // get current authentication state for user
      // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
      const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user);

      if (credentialState === appleAuth.State.AUTHORIZED && appleAuthRequestResponse.identityToken) {
        // use credentialState response to ensure the user is authenticated
        // user is authenticated
        await signIn(appleAuthRequestResponse.identityToken);
      } else throw new Error('Authentication denied');
    } catch (error) {
      toastError(error);
    }
  };

  const handleAppleSignInOnAndroid = async () => {
    try {
      // Generate secure, random values for state and nonce
      const rawNonce = uuid();
      const state = uuid();

      // Configure the request
      appleAuthAndroid.configure({
        // The Service ID you registered with Apple
        clientId: 'com.rabid.android.signin',

        // Return URL added to your Apple dev console. We intercept this redirect, but it must still match
        // the URL you provided to Apple. It can be an empty route on your backend as it's never called.
        redirectUri: 'https://rabid.var-meta.com/apple-auth/callback',

        // The type of response requested - code, id_token, or both.
        responseType: appleAuthAndroid.ResponseType.ALL,

        // The amount of user information requested from Apple.
        scope: appleAuthAndroid.Scope.ALL,

        // Random nonce value that will be SHA256 hashed before sending to Apple.
        nonce: rawNonce,

        // Unique state value used to prevent CSRF attacks. A UUID will be generated if nothing is provided.
        state,
      });

      // Open the browser window for user sign in
      const response = await appleAuthAndroid.signIn();
      if (response.id_token) {
        // Send the authorization code to your backend for verification
        await signIn(response.id_token);
      } else {
        throw new Error('Authentication denied');
      }
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <>
      <SignInSocialButton
        disabled={isPendingLogin || isPendingAddMethod || isPendingUpdateMethod}
        onPress={Platform.OS === 'android' ? handleAppleSignInOnAndroid : handleAppleSignInOnIOS}
        Icon={<Icons.Apple size={24} />}
        title='Apple'
        containerStyle={styles.container}
        isOnlyIcon={isOnlyIcon}
      />
      <AccountUpdatedModal
        isVisible={isAccountUpdatedModalVisible}
        onClose={() => setIsAccountUpdatedModalVisible(false)}
        token={accountUpdatedToken}
        provider='apple'
      />
    </>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralWhite,
  },
}));
