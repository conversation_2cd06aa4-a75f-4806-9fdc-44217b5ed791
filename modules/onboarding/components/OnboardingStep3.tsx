import { LinearGradient } from 'expo-linear-gradient';
import { Image, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

const WIDTH_SCREEN = UnistylesRuntime.screen.width;
const HEIGHT_SCREEN = UnistylesRuntime.screen.height;

export const OnboardingStep3 = () => {
  const { theme, styles } = useStyles(stylesheet);

  return (
    <LinearGradient
      colors={theme.colors.onboardingGradient1}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 0.5 }}
      style={[styles.container, { width: WIDTH_SCREEN, height: HEIGHT_SCREEN }]}
    >
      <SafeAreaView style={styles.safeView}>
        <View style={styles.logoView}>
          <Image source={require('@/assets/images/logo.png')} />
        </View>
      </SafeAreaView>

      <Image source={require('@/assets/images/onboarding-review-1.png')} style={styles.review1} />

      <Image source={require('@/assets/images/onboarding-review-3.png')} style={styles.review3} />

      <Image source={require('@/assets/images/onboarding-review-5.png')} style={styles.review4} />

      <Image source={require('@/assets/images/onboarding-review-6.png')} style={styles.review5} />

      <Image source={require('@/assets/images/onboarding-review-2.png')} style={styles.review2} />

      <Image source={require('@/assets/images/onboarding-review-4.png')} style={styles.review6} />
    </LinearGradient>
  );
};

const stylesheet = createStyleSheet({
  container: {
    alignItems: 'center',
    paddingBottom: 100,
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  text: {
    color: 'white',
  },
  safeView: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  logoView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
  },
  review1: {
    width: 309,
    height: 146,
    objectFit: 'contain',
    borderRadius: 12.94,
    top: 156,
    right: -13.03,
    position: 'absolute',
  },
  review2: {
    width: 230,
    height: 137,
    objectFit: 'contain',
    borderRadius: 9.63,
    top: 317,
    left: 70,
    position: 'absolute',
  },
  review3: {
    width: 226,
    height: 106,
    objectFit: 'contain',
    borderRadius: 9.47,
    top: 470,
    left: -25,
    position: 'absolute',
  },
  review4: {
    width: 77,
    height: 48,
    top: 210,
    left: 35,
    borderRadius: 8,
    position: 'absolute',
    resizeMode: 'cover',
  },
  review5: {
    width: 61,
    height: 38,
    top: 331,
    left: 282,
    borderRadius: 8,
    position: 'absolute',
    resizeMode: 'cover',
  },
  review6: {
    width: 159,
    height: 96,
    top: 470,
    left: 345,
    borderRadius: 6.7,
    position: 'absolute',
  },
});
