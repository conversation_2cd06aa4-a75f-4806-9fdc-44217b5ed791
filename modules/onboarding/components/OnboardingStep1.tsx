import { LinearGradient } from 'expo-linear-gradient';
import { Image, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

const WIDTH_SCREEN = UnistylesRuntime.screen.width;
const HEIGHT_SCREEN = UnistylesRuntime.screen.height;

type Props = {};

export const OnboardingStep1 = (props: Props) => {
  const { theme, styles } = useStyles(stylesheet);

  return (
    <LinearGradient
      colors={theme.colors.onboardingGradient1}
      style={[styles.container, { width: WIDTH_SCREEN, height: HEIGHT_SCREEN }]}
    >
      <SafeAreaView style={styles.safeView}>
        <View style={styles.logoView}>
          <Image source={require('@/assets/images/logo.png')} />
        </View>

        <Image source={require('@/assets/images/onboarding-step-1.png')} style={styles.bottomImage} />
      </SafeAreaView>
    </LinearGradient>
  );
};

const stylesheet = createStyleSheet({
  container: {
    alignItems: 'center',
    paddingBottom: 100,
    width: '100%',
    height: '100%',
  },
  text: {
    color: 'white',
  },
  gridImages: {
    width: '100%',
    objectFit: 'cover',
  },
  safeView: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  logoView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
    zIndex: 1,
  },
  bottomImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    objectFit: 'cover',
  },
  overlay: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    bottom: 0,
  },
});
