import { LinearGradient } from 'expo-linear-gradient';
import { Image, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

const WIDTH_SCREEN = UnistylesRuntime.screen.width;
const HEIGHT_SCREEN = UnistylesRuntime.screen.height;

export const OnboardingStep4 = () => {
  const { theme, styles } = useStyles(stylesheet);

  return (
    <LinearGradient
      colors={theme.colors.onboardingGradient1}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 0.5 }}
      style={[styles.container, { width: WIDTH_SCREEN, height: HEIGHT_SCREEN }]}
    >
      <SafeAreaView style={styles.safeView}>
        <View style={styles.logoView}>
          <Image source={require('@/assets/images/logo.png')} />
        </View>

        <View style={styles.mainImageBox}>
          <Image source={require('@/assets/images/onboarding-step-4.png')} style={styles.mainImage} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const stylesheet = createStyleSheet({
  container: {
    alignItems: 'center',
    paddingBottom: 100,
    width: '100%',
    height: '100%',
  },
  text: {
    color: 'white',
  },
  safeView: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  logoView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
  },
  mainImageBox: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    zIndex: -1,
  },
  mainImage: {
    width: '100%',
    objectFit: 'cover',
  },
});
