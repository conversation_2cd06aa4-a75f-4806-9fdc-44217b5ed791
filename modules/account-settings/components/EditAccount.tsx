import { Header } from '@/components/ui/Header';
import TextInput from '@/components/ui/TextInput';
import { useLocalSearchParams, router } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useState, useMemo } from 'react';
import { CustomButton } from '@/components/ui/CustomButton';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useUpdateUserProfileMeMutation } from '@/apis/user/mutations';
import { useRequestUpdateInforVerificationMutation } from '@/apis/auth';
import { useGetRegionInfoQuery } from '@/apis/user/queries';
import { phoneTransform } from '@/utils/phoneUtils';
import { toastError } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

const editAccountSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().optional(),
});

type EditAccountFormData = z.infer<typeof editAccountSchema>;

export const EditAccount = () => {
  const { styles } = useStyles(stylesheet);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [identifierType, setIdentifierType] = useState<string[]>([]);
  const queryClient = useQueryClient();

  const { email, phoneNumber } = useLocalSearchParams();
  const { data: userProfile } = useGetProfileQuery();
  const { data: regionInfo } = useGetRegionInfoQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { mutateAsync: updateUserProfile, isPending: isUpdatingUserProfile } = useUpdateUserProfileMeMutation();
  const { mutateAsync: requestVerification, isPending: isRequestingVerification } =
    useRequestUpdateInforVerificationMutation();

  const {
    control,
    handleSubmit,
    getValues,
    formState: { errors, isDirty },
  } = useForm<EditAccountFormData>({
    resolver: zodResolver(editAccountSchema),
    defaultValues: {
      email: (email as string) || '',
      phoneNumber: (phoneNumber as string) || '',
    },
  });

  const isLoadingSubmit = useMemo(
    () => isUpdatingUserProfile || isRequestingVerification,
    [isUpdatingUserProfile, isRequestingVerification]
  );

  const onSubmit = async (data: EditAccountFormData) => {
    try {
      if (!userProfile?.id) return;

      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const phoneNumber = data.phoneNumber ? phoneTransform(data.phoneNumber, regionInfo?.calling_code) : undefined;

      const { data: res } = await updateUserProfile({
        username: userProfile.username,
        email: data.email,
        dateOfBirth: userProfile.dateOfBirth,
        bio: userProfile.bio,
        avatar: userProfile.avatar,
        userId: userProfile.id,
        phoneNumber: phoneNumber || undefined,
      });

      let identifierType: string[] = [];

      if (res?.isEmailChanged && data.email) {
        identifierType.push(data.email);
      }

      if (res?.isPhoneChanged && phoneNumber) {
        identifierType.push(phoneNumber);
      }

      setIdentifierType(identifierType);

      if (identifierType.length > 0) {
        setShowConfirmationModal(true);
      } else {
        await Promise.all([
          queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
          queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
        ]);
        router.back();
      }
    } catch (error) {
      toastError(error);
    }
  };

  const handleModalCancel = () => {
    setShowConfirmationModal(false);
  };

  const handleConfirmChanges = async () => {
    try {
      if (identifierType.length > 0) {
        const remainingTime = 30;
        setShowConfirmationModal(false);
        router.push({
          pathname: '/(app)/verify-code',
          params: {
            identifierType: JSON.stringify(identifierType),
            remainingTime: Number(remainingTime),
            isChangeEmailOrPhone: 'true',
          },
        });
        for (const identifier of identifierType) {
          await requestVerification({
            identifier,
          });
        }
        return;
      }

      await Promise.all([
        queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
      ]);
      setShowConfirmationModal(false);
      router.back();
    } catch (error) {
      toastError(error);
      setShowConfirmationModal(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header title='Edit Account' isBack />
      <View style={styles.content}>
        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Controller
              control={control}
              name='email'
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label='Email'
                  placeholder='Enter your email'
                  onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                  onBlur={onBlur}
                  value={value}
                  keyboardType='email-address'
                  autoCapitalize='none'
                  error={errors.email?.message}
                />
              )}
            />
          </View>

          <View style={styles.inputContainer}>
            <Controller
              control={control}
              name='phoneNumber'
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label='Phone Number (Optional)'
                  placeholder='Input Phone Number'
                  onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                  onBlur={onBlur}
                  value={value || ''}
                  keyboardType='phone-pad'
                  error={errors.phoneNumber?.message}
                />
              )}
            />
          </View>
          <CustomButton
            textType='defaultBold'
            type='primary'
            onPress={handleSubmit(onSubmit)}
            disabled={!isDirty}
            isLoading={isLoadingSubmit}
            style={styles.submitButton}
          >
            Edit Account
          </CustomButton>
        </View>
      </View>
      <ConfirmationModal
        isVisible={showConfirmationModal}
        onClose={handleModalCancel}
        onCancel={handleModalCancel}
        onConfirm={handleConfirmChanges}
        title='Are you sure you want to update these information?'
        description='Updating email or phone number may affect how you sign in to your account'
        cancelText='Cancel'
        confirmText='Edit Profile'
        isLoading={isLoadingSubmit}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top + 16,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 56,
    paddingBottom: 40,
  },
  form: {
    flex: 1,
    gap: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 9999,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 'auto',
    width: 320,
    height: 48,
    marginTop: 28,
  },
  submitButtonText: {
    color: theme.colors.neutralBackground,
    fontSize: 16,
  },
}));
