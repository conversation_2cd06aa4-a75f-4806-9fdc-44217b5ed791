import { ExpoImage } from '@/components/ui/Image';
import { Pressable, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useGetBannerQuery } from '@/apis/banner';
import { Skeleton } from 'moti/skeleton';

type Props = {};

export const ShowsBanner = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: bannerData, isLoading } = useGetBannerQuery();

  const imageSource = bannerData?.data?.imageUrl
    ? { uri: bannerData.data.imageUrl }
    : require('@/assets/images/show-banner.png');

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Skeleton width={'100%'} height={240} radius={12} />
      </View>
    );
  }

  return (
    <Pressable style={styles.container}>
      <ExpoImage source={imageSource} style={styles.image} />
    </Pressable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    height: 240,
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 24,
    marginBottom: 16,
    borderWidth: 1,
  },
  image: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
}));
