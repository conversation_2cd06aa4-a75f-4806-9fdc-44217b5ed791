import { Header } from '@/components/ui/Header';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { BrowserShow } from './components/tabs/BrowserShow';
import { CommunityFeeds } from './components/tabs/CommunityFeeds';
import { TouchableOpacity, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabItem } from '@/components/collapsing-tabs/TabItem';
import { Spacer } from '@/components/Spacer';

export default function Home() {
  const { styles } = useStyles(stylesheet);

  const handlePressDirectReward = () => {
    router.push('/(app)/spread-vibes');
  };

  const renderActionReward = (
    <TouchableOpacity activeOpacity={0.7} onPress={handlePressDirectReward}>
      <Icons.Gift size={25} color='#fff' />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header rightAction={renderActionReward} />
      </View>

      <Spacer height={15} />

      <TabContainer containerStyle={styles.tabContainer} headerContainerStyle={styles.headerContainer}>
        <TabItem tabName='Trending' label={'Trending'}>
          <BrowserShow />
        </TabItem>

        <TabItem tabName='Feed' label={'Community Feed'}>
          <CommunityFeeds />
        </TabItem>
      </TabContainer>
    </View>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingTop: rt.insets.top,
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: theme.colors.neutralBackground,
  },
  logoutTextStyle: {
    color: theme.colors.neutralBackground,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
}));
