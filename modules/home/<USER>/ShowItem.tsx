import { ExpoImage } from '@/components/ui/Image';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TouchableOpacity } from 'react-native';
import { IPodcast } from '@/apis/podcast';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  item: IPodcast;
  itemSize: number;
};

export const ShowItem = ({ item, itemSize }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handlePress = async () => {
    await showDetailDirect(queryClient, item.id.toString());
  };

  return (
    <TouchableOpacity
      style={[styles.container, { width: itemSize, height: itemSize }]}
      activeOpacity={0.7}
      onPress={handlePress}
    >
      <ExpoImage source={{ uri: item.imageUrl }} style={styles.showImage} />
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  showImage: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
}));
