import { router, useLocalSearchParams } from 'expo-router';
import { useRef, useState } from 'react';
import { KeyboardAvoidingView, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import {
  useUpdateCommentMutation,
  useUpdateEpisodeCommentMutation,
  useUploadCommentImageMutation,
} from '@/apis/comment/mutations';
import { useGetCommentByIdQuery, useGetEpisodeCommentByIdQuery } from '@/apis/comment/queries';
import { useGetEpisodeByIdQuery } from '@/apis/episode/queries';
import { useGetPodcastByIdQuery } from '@/apis/podcast';
import { useUploadImageToS3 } from '@/apis/user';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { toastError, toastSuccess } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import PostForm, { PostFormData, PostFormRef } from '../../components/PostForm';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

// Mock post data (in a real app this would come from an API or context)
const mockPost = {
  title: 'So Good Shows',
  comment: 'This show is very very good',
  images: [], // Would typically have the image URIs here
};

function EditPost() {
  const { styles } = useStyles(stylesheet);

  const [isSubmitting, setIsSubmitting] = useState(true); // Start with true for edit form
  const localParams = useLocalSearchParams();
  const postId = (localParams?.postId || '') as string;
  const podcastId = (localParams?.podcastId || '') as string;
  const episodeId = (localParams?.episodeId || '') as string;
  const queryClient = useQueryClient();
  const formRef = useRef<PostFormRef>(null);

  // Determine if this is for podcast or episode
  const isForPodcast = !!podcastId;
  const isForEpisode = !!episodeId;

  const { data: podcast } = useGetPodcastByIdQuery({ podcastId }, { enabled: isForPodcast });
  const { data: episode } = useGetEpisodeByIdQuery(episodeId, { enabled: isForEpisode });

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  // Use appropriate comment query based on type
  const { data: podcastPost } = useGetCommentByIdQuery(postId, 'local', {
    enabled: isForPodcast,
  });
  const { data: episodePost } = useGetEpisodeCommentByIdQuery(postId, 'local', {
    enabled: isForEpisode,
  });

  // Use the appropriate post data and refetch function
  const post = isForPodcast ? podcastPost : episodePost;

  const { mutateAsync: updatePodcastComment, isPending: isPendingUpdatePodcastComment } = useUpdateCommentMutation();
  const { mutateAsync: updateEpisodeComment, isPending: isPendingUpdateEpisodeComment } =
    useUpdateEpisodeCommentMutation();
  const { mutateAsync: requestUploadImage, isPending: isPendingRequestUploadImage } = useUploadCommentImageMutation();
  const { mutateAsync: uploadImageToS3, isPending: isUploadingToS3 } = useUploadImageToS3();

  const handleSubmit = async (data: PostFormData) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const oldImages = [];
      const newImagesToUpload = [];

      for (const image of data.images || []) {
        if (post?.images?.includes(image)) {
          oldImages.push(image);
        } else {
          newImagesToUpload.push(image);
        }
      }

      const uploadedUrl = [...oldImages];

      if (newImagesToUpload.length > 0) {
        const uploadToS3Info = await requestUploadImage({ count: newImagesToUpload.length });
        try {
          const result = await Promise.all(
            newImagesToUpload.map(async (image, index) => {
              const presignedUrl = uploadToS3Info[index]?.presigned;
              const publicUrl = uploadToS3Info[index]?.url;

              if (!presignedUrl || !publicUrl) {
                throw new Error('Invalid upload URL received');
              }

              // Upload the image to S3
              await uploadImageToS3({
                file: image,
                url: presignedUrl,
              });

              return publicUrl;
            })
          );
          uploadedUrl.push(...result);
        } catch (error) {
          throw new Error('Edited post unsuccessfully');
        }
      }

      if (isForPodcast) {
        await updatePodcastComment({
          id: postId,
          podcastId,
          content: data.comment?.trim(),
          title: data.title?.trim(),
          images: uploadedUrl,
        });
      } else if (isForEpisode) {
        await updateEpisodeComment({
          id: postId,
          episodeId,
          content: data.comment?.trim(),
          title: data.title?.trim(),
          images: uploadedUrl,
        });
      }

      toastSuccess({ description: 'Edited post successfully' });
      router.back();
    } catch (error) {
      toastError(error);
    }
  };

  const triggerSubmit = () => {
    formRef.current?.submit();
  };

  const isPosting =
    isPendingRequestUploadImage ||
    isPendingUpdatePodcastComment ||
    isPendingUpdateEpisodeComment ||
    isUploadingToS3 ||
    isSubmitting;

  return (
    <KeyboardAvoidingView behavior='padding' style={styles.content}>
      <View style={styles.container}>
        <Header
          title='Edit Post'
          titleStyle={styles.titleStyle}
          leftAction={
            <TouchableOpacity onPress={() => router.back()}>
              <ThemedText type='small' style={styles.cancelText}>
                Cancel
              </ThemedText>
            </TouchableOpacity>
          }
          rightAction={
            <CustomButton
              style={[styles.editButton, isPosting && styles.editButtonDisabled]}
              isLoading={isPosting}
              onPress={triggerSubmit}
              textStyle={styles.editButtonText}
            >
              {isPosting ? '' : 'Edit Post'}
            </CustomButton>
          }
        />

        <View style={styles.content}>
          <PostForm
            ref={formRef}
            podcast={podcast}
            episode={episode}
            initialData={post}
            onSubmit={handleSubmit}
            onFormSubmittingChange={setIsSubmitting}
          />
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
    paddingHorizontal: 16,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelText: {
    color: theme.colors.neutralLightGrey,
  },
  editButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 7,
    minHeight: 38,
  },
  editButtonDisabled: {
    backgroundColor: theme.colors.neutralGrey,
  },
  editButtonText: {
    color: theme.colors.background,
    ...theme.fw600,
    fontSize: 12,
    lineHeight: 24,
  },
  titleStyle: {
    ...theme.fw500,
    fontSize: 16,
    lineHeight: 24,
  },
}));

export default EditPost;
