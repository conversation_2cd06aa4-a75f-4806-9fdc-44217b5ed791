import { Header } from '@/components/ui/Header';
import { Zoomable } from '@likashefqet/react-native-image-zoom';
import { useLocalSearchParams } from 'expo-router';
import { FlatList, View } from 'react-native';
import { Gesture, GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedText } from '@/components/ThemedText';
import { router } from 'expo-router';
import { useCallback, useState } from 'react';
import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { ListRenderItem } from 'react-native';
import { ExpoImageAnimated } from '@/components/ui/Image';

const rt = UnistylesRuntime;
const ImagesScreen = () => {
  const { urls } = useLocalSearchParams<{
    urls: string;
  }>();
  const { activeIndex } = useLocalSearchParams<{
    activeIndex: string;
  }>();
  const urlsConvert = JSON.parse(urls) as string[];
  const isEnablePaginate = urlsConvert.length > 1;
  const translateY = useSharedValue(0);

  const [currentIndex, setCurrentIndex] = useState(Number(activeIndex ?? 0));

  const { styles } = useStyles(stylesheet);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const handleCloseDialog = () => {
    router.dismiss();
  };

  const panGesture = Gesture.Pan()
    .onUpdate((e) => {
      translateY.value = e.translationY;
    })
    .onEnd(() => {
      if (translateY.value > 75) {
        ('worklet');
        runOnJS(handleCloseDialog)();
      } else {
        translateY.value = withTiming(0, { duration: 100 });
      }
    });

  const renderItem = useCallback<ListRenderItem<string>>(
    ({ item, index }) => (
      <Animated.View style={styles.imageContainer}>
        <Zoomable minScale={1} maxScale={5} maxPanPointers={2} doubleTapScale={2} isSingleTapEnabled isDoubleTapEnabled>
          <ExpoImageAnimated style={styles.image} source={{ uri: item }} contentFit='contain' transition={0} />
        </Zoomable>
      </Animated.View>
    ),
    [styles]
  );

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: rt.screen.width,
      offset: rt.screen.width * index,
      index,
    }),
    []
  );

  const keyExtractor = useCallback((item: string, index: number) => index.toString(), []);

  const handleScroll = useCallback((event: any) => {
    const newIndex = Math.floor(event.nativeEvent.contentOffset.x / event.nativeEvent.layoutMeasurement.width);
    setCurrentIndex(newIndex);
  }, []);

  return (
    <GestureHandlerRootView>
      <SafeAreaView style={styles.headerContainer}>
        <Header
          isClose
          closeStyle={styles.closeStyle}
          title=''
          leftStyle={{ width: 50 }}
          leftAction={
            isEnablePaginate ? (
              <View style={styles.paginate}>
                <ThemedText type='tiny'>
                  {currentIndex + 1}/{urlsConvert.length}
                </ThemedText>
              </View>
            ) : null
          }
        />
      </SafeAreaView>

      {/* <GestureDetector gesture={panGesture}> */}
      <Animated.View style={[styles.container, animatedStyle]}>
        <FlatList
          data={urlsConvert}
          showsHorizontalScrollIndicator={false}
          horizontal
          pagingEnabled
          initialScrollIndex={currentIndex}
          style={{ flex: 1 }}
          // contentContainerStyle={{ flexGrow: 1 }}
          renderItem={renderItem}
          getItemLayout={getItemLayout}
          keyExtractor={keyExtractor}
          onMomentumScrollEnd={handleScroll}
        />
      </Animated.View>
      {/* </GestureDetector> */}
    </GestureHandlerRootView>
  );
};

export default ImagesScreen;

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    // flex: 1,
    height: '100%',
    backgroundColor: theme.colors.neutralBackground,
  },
  image: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  headerContainer: {
    paddingHorizontal: 24,
    position: 'absolute',
    zIndex: 9999,
  },
  imageContainer: {
    width: rt.screen.width,
    height: rt.screen.height,
  },
  paginate: {
    backgroundColor: theme.colors.neutralDarkGrey,
    borderRadius: 999,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 5,
  },
  closeStyle: {
    backgroundColor: theme.colors.neutralDarkGrey,
    borderRadius: 999,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 5,
  },
}));
