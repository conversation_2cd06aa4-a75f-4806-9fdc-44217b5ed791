import { useState } from 'react';
import { NativeSyntheticEvent, TextLayoutEventData, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from './ThemedText';

type Props = {
  text: string;
  textEndOfLine: string;
  textEndOfLineWidth: number;
  numberOfLines: number;
  endOfLineStyle?: ViewStyle;
};

const initTruncateWidth = 23.617624282836914;

export const CustomTextWithEndOfLine = ({
  text,
  textEndOfLine,
  textEndOfLineWidth,
  numberOfLines,
  endOfLineStyle,
}: Props) => {
  const { styles } = useStyles(stylesheet);

  const [endOfLineTopPosition, setEndOfLineTopPosition] = useState(0);
  const [textEndOfLineWidthFull] = useState(textEndOfLineWidth + initTruncateWidth);
  const [textTruncateFirstWidth, setTextTruncateFirstWidth] = useState(0);
  const [textTruncateLastWidth, setTextTruncateLastWidth] = useState(0);

  const isShowTruncate = textTruncateFirstWidth - textEndOfLineWidthFull < textTruncateLastWidth;
  const truncateWidth = isShowTruncate ? textEndOfLineWidthFull : 0;
  const leftPositionEndOfLine = textTruncateLastWidth - truncateWidth < 0 ? 0 : textTruncateLastWidth - truncateWidth;

  const onTextLayout = ({ nativeEvent: { lines }, currentTarget }: NativeSyntheticEvent<TextLayoutEventData>) => {
    const endLine = lines[lines.length - 1];
    const endLineWidth = endLine.width;

    currentTarget.measure((x, y, width) => {
      setEndOfLineTopPosition(endLine.y);

      setTextTruncateFirstWidth(width);
      setTextTruncateLastWidth(endLineWidth);
    });
  };

  return (
    <View style={styles.container}>
      <ThemedText type='tinyMedium' style={styles.text} numberOfLines={numberOfLines} onTextLayout={onTextLayout}>
        {text}
      </ThemedText>

      <View
        style={[
          styles.endOfLineBox,
          endOfLineStyle,
          {
            left: leftPositionEndOfLine,
            top: endOfLineTopPosition,
          },
        ]}
      >
        <ThemedText type='tinyMedium' numberOfLines={1} style={[styles.endOfLineText]}>
          {isShowTruncate ? '... ' : ' '}• {textEndOfLine}
        </ThemedText>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  text: {
    color: theme.colors.whiteOpacity56,
    lineHeight: 18,
    alignItems: 'center',
  },
  endOfLineText: {
    color: theme.colors.whiteOpacity56,
    lineHeight: 18,
    alignItems: 'center',
  },
  endOfLineBox: {
    backgroundColor: theme.colors.neutralBackground,
    position: 'absolute',
  },
}));
