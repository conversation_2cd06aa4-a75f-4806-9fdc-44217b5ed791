import { useCollapsingTabsContext } from '@/contexts/app.context';
import { FlatList, ScrollView } from 'react-native';
import {
  scrollTo,
  useAnimatedRef,
  useAnimatedScrollHandler,
  useDerivedValue,
  useSharedValue,
} from 'react-native-reanimated';
import { useTabContext } from './tab.context';

export const useAnimateList = <T extends ScrollView | FlatList>() => {
  const { tabName: tabNameItem } = useTabContext();
  const { tabName, currentScroll, headerDiff, isScrolling } = useCollapsingTabsContext();

  const ref = useAnimatedRef<T>();

  const isTabActive = tabName === tabNameItem;

  const scrollValue = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollValue.value = event.contentOffset.y;

      if (isTabActive) {
        currentScroll.value = event.contentOffset.y;
      }
    },
    onBeginDrag: () => {
      isScrolling.value = 1;
    },
    onMomentumEnd: () => {
      isScrolling.value = 0;
    },
  });

  useDerivedValue(() => {
    const shouldSync = isScrolling.value === 0 || Math.floor(currentScroll.value) % 20 === 0;
    if (!shouldSync || isTabActive) return;

    const isEnableSync = scrollValue.value <= headerDiff || currentScroll.value <= headerDiff;
    if (!isEnableSync) return;

    const scrollToValue =
      currentScroll.value <= headerDiff ? currentScroll.value : Math.max(scrollValue.value, headerDiff);

    scrollTo(ref, 0, scrollToValue, false);
  });

  return {
    ref,
    scrollHandler,
  };
};
