import { memo, PropsWithChildren } from 'react';
import { TabContext } from './TabContext';
import { TabNavigator } from './TabNavigator';
import { StyleSheet, ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';
import { TabsWithProps } from './TabItem';

type Props = {
  renderHeader?: React.ReactNode | (() => React.JSX.Element);
  containerStyle?: ViewStyle;
  headerContainerStyle?: ViewStyle;
  initHeaderHeight?: number;
  minHeaderHeight?: number;
  children: React.ReactElement<TabsWithProps> | React.ReactElement<TabsWithProps>[];
};

export const TabContainer = memo(
  ({
    children,
    renderHeader,
    containerStyle,
    headerContainerStyle,
    initHeaderHeight,
    minHeaderHeight,
  }: PropsWithChildren<Props>) => {
    return (
      <TabContext initHeaderHeight={initHeaderHeight} minHeaderHeight={minHeaderHeight}>
        <Animated.View style={[styles.container, containerStyle]}>
          <TabNavigator headerContainerStyle={headerContainerStyle} renderHeader={renderHeader}>
            {children}
          </TabNavigator>
        </Animated.View>
      </TabContext>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
