import React, { PropsWithChildren, useCallback } from 'react';
import { useCollapsingTabsContext } from '@/contexts/app.context';
import { ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import PagerView, { PagerViewOnPageScrollEvent } from 'react-native-pager-view';
import { TabItem, TabsWithProps } from './TabItem';
import { useTabProps } from './hooks/useTabProps';
import { TabHeader } from './TabHeader';
import { TabBar } from './TabBar';

const AnimatedPagerView = Animated.createAnimatedComponent(PagerView);

type Props = {
  renderHeader?: React.ReactNode | (() => React.JSX.Element);
  headerContainerStyle?: ViewStyle;
  tabBarStyle?: ViewStyle;
  children: React.ReactElement<TabsWithProps> | React.ReactElement<TabsWithProps>[];
};

export const TabNavigator = ({
  children,
  renderHeader,
  headerContainerStyle: headerContainerStyleOvr,
}: PropsWithChildren<Props>) => {
  const { styles } = useStyles(stylesheet);

  const [tabOptions, tabNamesArray] = useTabProps(children, TabItem);

  const { tabBarStyle, progressIndicator, onIndexChange, onTabNameChange, tabIndex, refPager } =
    useCollapsingTabsContext();

  const handlePageScroll = useCallback(
    (event: PagerViewOnPageScrollEvent) => {
      const { offset, position } = event.nativeEvent;
      progressIndicator.value = position + offset;
    },
    [progressIndicator]
  );

  const handlePageSelected = useCallback(
    (event: any) => {
      onIndexChange(event.nativeEvent.position);
      onTabNameChange(tabNamesArray[event.nativeEvent.position]);
    },
    [onIndexChange, onTabNameChange, tabNamesArray]
  );

  return (
    <>
      <TabHeader renderHeader={renderHeader} headerContainerStyle={headerContainerStyleOvr} />

      <Animated.View style={[styles.tabsContainer, tabBarStyle]}>
        <TabBar
          tabOptions={tabOptions}
          tabNamesArray={tabNamesArray}
          onIndexChange={onIndexChange}
          onTabNameChange={onTabNameChange}
        />
      </Animated.View>

      <AnimatedPagerView
        initialPage={tabIndex}
        ref={refPager}
        style={{ flex: 1 }}
        onPageSelected={handlePageSelected}
        onPageScroll={handlePageScroll}
      >
        {tabNamesArray.map((tabName) => {
          const { index, ...props } = tabOptions.get(tabName)!;
          return <TabItem key={tabName} {...props} tabName={tabName} />;
        })}
      </AnimatedPagerView>
    </>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  tabBarStyle: {
    backgroundColor: theme.colors.neutralBackground,
    // width: 'auto',
  },
  tabBarContentContainerStyle: {
    // width: 'auto',
  },
  tabBarIndicatorContainerStyle: {},
  tabBarIndicatorStyle: {
    backgroundColor: theme.colors.primary,
  },
  tabBarLabelStyle: {
    ...theme.fw500,
    fontSize: 16,
  },
  tabsContainer: {
    backgroundColor: theme.colors.neutralBackground,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  tabBarItemStyle: {
    width: 'auto',
  },
  fullFlex: {
    flex: 1,
  },
}));
