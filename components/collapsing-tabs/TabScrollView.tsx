import { useCollapsingTabsContext } from '../../contexts/app.context';
import Animated from 'react-native-reanimated';
import { useAnimateList } from './useAnimateList';
import { PropsWithChildren, Ref } from 'react';
import { ScrollViewAnimate, ScrollViewAnimateProps } from '../ScrollViewAnimate';

type Props = {} & ScrollViewAnimateProps;

export const TabScrollView = ({ children, ...props }: PropsWithChildren<Props>) => {
  const { sharedProps } = useCollapsingTabsContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  return (
    <ScrollViewAnimate
      ref={ref as Ref<Animated.ScrollView>}
      {...props}
      onScroll={scrollHandler}
      {...sharedProps}
      contentContainerStyle={
        {
          ...(contentContainerStyle && typeof contentContainerStyle === 'object' ? contentContainerStyle : {}),
          ...(props.contentContainerStyle && typeof props.contentContainerStyle === 'object'
            ? props.contentContainerStyle
            : {}),
        } as any
      }
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
    >
      {children}
    </ScrollViewAnimate>
  );
};
