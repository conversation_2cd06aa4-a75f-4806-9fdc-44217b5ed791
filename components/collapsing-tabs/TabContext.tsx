import { PropsWithChildren, useCallback, useMemo, useRef, useState } from 'react';
import { CollapsingTabsContextProvider } from '@/contexts/app.context';
import { ScrollViewProps, StyleProp, StyleSheet, useWindowDimensions, ViewProps, ViewStyle } from 'react-native';
import { useAnimatedStyle, useDerivedValue, useSharedValue } from 'react-native-reanimated';
import PagerView from 'react-native-pager-view';

const TAB_BAR_HEIGHT = 48;

type Props = {
  initHeaderHeight?: number;
  minHeaderHeight?: number;
};

export const TabContext = ({ children, initHeaderHeight = 0, minHeaderHeight = 0 }: PropsWithChildren<Props>) => {
  // const { bottom } = useSafeAreaInsets();
  const { height: screenHeight } = useWindowDimensions();

  const [tabIndex, setTabIndex] = useState(0);
  const [tabName, setTabName] = useState('');
  const [headerHeight, setHeaderHeight] = useState(initHeaderHeight);

  const refPager = useRef<PagerView>(null);

  const currentScroll = useSharedValue(0);
  const isScrolling = useSharedValue(0);
  const progressIndicator = useSharedValue(0);

  const headerConfig = useMemo(
    () => ({
      heightCollapsed: minHeaderHeight,
      heightExpanded: headerHeight,
    }),
    [headerHeight, minHeaderHeight]
  );

  const { heightCollapsed, heightExpanded } = headerConfig;

  const headerDiff = heightExpanded - heightCollapsed;

  const rendered = headerHeight > 0;

  const headerHeightDiff = heightExpanded - heightCollapsed;
  const translateY = useDerivedValue(() => -Math.min(currentScroll.value, headerHeightDiff), [headerHeightDiff]);

  const tabBarAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }, { translateX: 0 }],
  }));

  const tabBarStyle = useMemo<StyleProp<ViewStyle>>(
    () => [
      rendered ? styles.tabBarContainer : undefined,
      { top: rendered ? headerHeight : undefined },
      tabBarAnimatedStyle,
    ],
    [rendered, headerHeight, tabBarAnimatedStyle]
  );

  const contentContainerStyle = useMemo<StyleProp<ViewStyle>>(
    () => ({
      paddingTop: rendered ? headerHeight + TAB_BAR_HEIGHT : 0,
      // paddingBottom: bottom,
      minHeight: screenHeight + headerDiff,
    }),
    [headerHeight, rendered, headerDiff, screenHeight]
  );

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
      // opacity: interpolate(translateY.value, [-headerDiff, 0], [0, 1]),
    };
  }, [translateY.value]);

  const headerContainerStyle = useMemo<StyleProp<ViewStyle>>(
    () => [rendered ? styles.headerContainer : undefined, headerAnimatedStyle],
    [rendered, headerAnimatedStyle]
  );

  const sharedProps = useMemo<Partial<ScrollViewProps>>(
    () => ({
      contentContainerStyle,
      scrollIndicatorInsets: { top: headerHeight },
      scrollEventThrottle: 8,
    }),
    [contentContainerStyle, headerHeight]
  );

  const handleHeaderLayout = useCallback<NonNullable<ViewProps['onLayout']>>(
    (event) => setHeaderHeight(event.nativeEvent.layout.height),
    []
  );

  return (
    <CollapsingTabsContextProvider
      value={{
        sharedProps,
        currentScroll,
        tabName,
        tabIndex,
        headerDiff,
        headerContainerStyle,
        tabBarStyle,
        isScrolling,
        progressIndicator,
        refPager,
        translateY,
        handleHeaderLayout,
        onIndexChange: setTabIndex,
        onTabNameChange: setTabName,
      }}
    >
      {children}
    </CollapsingTabsContextProvider>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    top: 0,
    left: 0,
    right: 0,
    position: 'absolute',
    zIndex: 1,
  },
  headerContainer: {
    top: 0,
    left: 0,
    right: 0,
    position: 'absolute',
    zIndex: 1,
  },
});
