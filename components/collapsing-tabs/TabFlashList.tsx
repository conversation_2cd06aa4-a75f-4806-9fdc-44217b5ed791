import { useCollapsingTabsContext } from '@/contexts/app.context';
import { useAnimateList } from './useAnimateList';
import { FlashListProps } from '@shopify/flash-list';
import { RefObject, useRef } from 'react';
import { ViewabilityConfig } from 'react-native';
import { FlashListAnimate } from '../FlashListAnimate';

type Props<T> = {} & FlashListProps<T>;

export const TabFlashList = <T,>(props: Props<T>) => {
  const { sharedProps } = useCollapsingTabsContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  const viewabilityConfig = useRef<ViewabilityConfig>({
    waitForInteraction: false,
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 1000,
  }).current;

  return (
    <FlashListAnimate
      {...props}
      ref={ref as RefObject<any>}
      onScroll={scrollHandler}
      {...(sharedProps as any)}
      contentContainerStyle={{
        ...(contentContainerStyle && typeof contentContainerStyle === 'object' ? contentContainerStyle : {}),
        ...props.contentContainerStyle,
      }}
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
      viewabilityConfig={viewabilityConfig}
    />
  );
};
