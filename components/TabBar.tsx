import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { router, useGlobalSearchParams, usePathname } from 'expo-router';
import { createStyleSheet, UnistylesRuntime, useStyles } from 'react-native-unistyles';
import TabBarButton from './TabBarButton';
import Animated, { useAnimatedStyle, useSharedValue, withSequence, withSpring } from 'react-native-reanimated';
import { View } from 'react-native';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { useUserStore } from '@/store/user';

type Props = {} & BottomTabBarProps;

const rt = UnistylesRuntime;
const TabBar = (props: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const { state, descriptors, navigation, insets } = props;
  const tabPositionX = useSharedValue(0);
  const indicatorX = useSharedValue(1);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const setIsShowWarningRestricted = useUserStore.use.setIsShowWarningRestricted();

  const buttonWidth = rt.screen.width / state.routes.length;
  const paramsSearch = useGlobalSearchParams<{ podcastId: string; episodeId: string }>();
  const currentStateIndex = navigation.getState().index;

  const pathname = usePathname();

  const indicatorBoxAnimate = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateX: tabPositionX.value,
        },
      ],
    };
  });

  const indicatorAnimate = useAnimatedStyle(() => {
    return {
      transform: [{ scaleX: indicatorX.value }],
    };
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    tabPositionX.value = withSpring(currentStateIndex * buttonWidth);
    indicatorX.value = withSequence(withSpring(0.8), withSpring(1));
  }, [currentStateIndex, buttonWidth]);

  return (
    <View style={styles.tabbar}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options?.tabBarLabel || options?.title;

        if (['_sitemap', '+not-found'].includes(route.name)) return null;

        const isFocused = state.index === index;

        const onPress = () => {
          if (currentStateIndex === 0) {
            queryClient.resetQueries({ queryKey: queryKeys.podcasts.communityFeed() });
          }

          if (route.name === 'interact') {
            const isRestricted = onCheckAccountRestricted();
            if (isRestricted) {
              setIsShowWarningRestricted(true);
              return;
            }

            if (pathname.includes('/discover/podcast/')) {
              if (paramsSearch?.podcastId)
                return router.push({
                  pathname: '/(app)/podcast/[podcastId]/add-post',
                  params: {
                    podcastId: paramsSearch?.podcastId,
                  },
                });
            }

            if (pathname.includes('/discover/episode/')) {
              if (paramsSearch?.episodeId)
                return router.push({
                  pathname: '/(app)/episode/[episodeId]/add-post',
                  params: {
                    episodeId: paramsSearch?.episodeId,
                  },
                });
            }

            return router.push({
              pathname: '/(app)/discover-search',
              params: {
                searchAction: 'create-post',
              },
            });
          }

          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TabBarButton
            key={route.name}
            onPress={onPress}
            onLongPress={onLongPress}
            isFocused={isFocused}
            routeName={route.name}
            color={isFocused ? options.tabBarActiveTintColor! : theme.colors.neutralLightGrey}
            label={label}
            tabBarIcon={options.tabBarIcon}
            iconScale={indicatorX}
          />
        );
      })}

      <Animated.View style={[styles.indicatorBox, { width: buttonWidth }, indicatorBoxAnimate]}>
        <Animated.View style={[styles.indicator, { width: buttonWidth - 25 }, indicatorAnimate]} />
      </Animated.View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  tabbar: {
    bottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralBackground,

    shadowColor: 'black',
    shadowOffset: { width: 0, height: 10 },
    shadowRadius: 10,
    shadowOpacity: 0.1,
    zIndex: 9999,
    width: '100%',
    paddingBottom: rt.insets.bottom,
  },
  indicatorBox: {
    position: 'absolute',
    top: 60,
    height: 4,
    borderRadius: 999,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  indicator: {
    backgroundColor: theme.colors.primary,
    borderRadius: 999,
    height: 5,
  },
}));

export default TabBar;
