import { Text, type TextProps } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
    | 'default'
    | 'defaultMedium'
    | 'title'
    | 'titleSemiBold'
    | 'defaultSemiBold'
    | 'defaultBold'
    | 'subtitle'
    | 'subtitleMedium'
    | 'subtitleSemiBold'
    | 'link'
    | 'smallNormal'
    | 'small'
    | 'smallSemiBold'
    | 'inputError'
    | 'tiny'
    | 'tinyMedium'
    | 'tinySemiBold';
};

export function ThemedText({ style, lightColor, darkColor, type = 'default', ...rest }: ThemedTextProps) {
  const { styles } = useStyles(stylesheet);

  return (
    <Text
      pointerEvents='box-only'
      allowFontScaling={false}
      style={[
        styles.text,
        type === 'default' ? styles.default : undefined,
        type === 'defaultMedium' ? styles.defaultMedium : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'defaultBold' ? styles.defaultBold : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'titleSemiBold' ? styles.titleSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'subtitleMedium' ? styles.subtitleMedium : undefined,
        type === 'subtitleSemiBold' ? styles.subtitleSemiBold : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'tiny' ? styles.tiny : undefined,
        type === 'tinyMedium' ? styles.tinyMedium : undefined,
        type === 'tinySemiBold' ? styles.tinySemiBold : undefined,
        type === 'smallNormal' ? styles.smallNormal : undefined,
        type === 'small' ? styles.small : undefined,
        type === 'smallSemiBold' ? styles.smallSemiBold : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const stylesheet = createStyleSheet((theme) => ({
  text: {
    color: theme.colors.neutralWhite,
  },
  tiny: {
    fontSize: 12,
    lineHeight: 16,
    ...theme.fw400,
  },
  tinyMedium: {
    fontSize: 12,
    lineHeight: 24,
    ...theme.fw500,
  },
  tinySemiBold: {
    fontSize: 12,
    lineHeight: 24,
    ...theme.fw600,
  },
  smallNormal: {
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw400,
  },
  small: {
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw500,
  },
  smallSemiBold: {
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw600,
  },
  default: {
    fontSize: 16,
    lineHeight: 24,
  },
  defaultMedium: {
    fontSize: 16,
    lineHeight: 24,
    ...theme.fw500,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    ...theme.fw600,
  },
  defaultBold: {
    fontSize: 16,
    lineHeight: 24,
    ...theme.fw700,
  },
  link: {
    fontSize: 16,
    lineHeight: 30,
    color: '#0a7ea4',
  },
  title: {
    fontSize: 28,
    ...theme.fw700,
    lineHeight: 32,
  },
  titleSemiBold: {
    fontSize: 28,
    ...theme.fw600,
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 20,
    ...theme.fw700,
  },
  subtitleMedium: {
    fontSize: 20,
    lineHeight: 28,
    ...theme.fw500,
  },
  subtitleSemiBold: {
    fontSize: 20,
    lineHeight: 28,
    ...theme.fw600,
  },
}));
