import { ViewStyle } from 'react-native';
import { MaterialTabBar, MaterialTabBarProps, MaterialTabItem, TabBarProps } from 'react-native-collapsible-tab-view';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  containerStyle?: ViewStyle;
} & TabBarProps &
  MaterialTabBarProps<string>;

export const TabBarCollapsibleTabView = (props: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  return (
    <Animated.View style={[styles.tabsContainer, props.containerStyle]}>
      <Animated.View>
        <MaterialTabBar
          {...props}
          scrollEnabled
          inactiveColor={theme.colors.neutralLightGrey}
          activeColor={theme.colors.neutralWhite}
          labelStyle={[styles.labelStyle, props.labelStyle]}
          indicatorStyle={[styles.indicatorStyle, props.indicatorStyle]}
          contentContainerStyle={{ gap: 20 }}
          TabItemComponent={(props) => (
            <MaterialTabItem
              {...props}
              label={props.label || props.name}
              pressColor='transparent'
              style={{ minWidth: 80 }}
            />
          )}
        />
      </Animated.View>
    </Animated.View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: theme.colors.neutralBackground,
  },
  indicatorStyle: {
    backgroundColor: theme.colors.neutralWhite,
  },
  labelStyle: {},
}));
