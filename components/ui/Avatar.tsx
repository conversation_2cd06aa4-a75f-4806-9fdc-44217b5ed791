import { ImageStyle, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ExpoImage } from './Image';

type Props = {
  image?: string;
  size?: number;
  containerStyle?: ViewStyle;
  imageStyle?: ImageStyle;
};

const Avatar = ({ containerStyle, imageStyle, image, size = 40 }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={[styles.container, containerStyle]}>
      <ExpoImage
        source={image || require('@/assets/images/default-avatar.png')}
        style={[styles.avatar, { width: size, height: size }, imageStyle]}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  avatar: {
    borderRadius: 999,
  },
  container: {
    borderRadius: 999,
    overflow: 'hidden',
  },
}));

export { Avatar };
