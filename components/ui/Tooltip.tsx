import React, { useState, useRef } from 'react';
import { View, TouchableOpacity, Dimensions } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ModalRn } from '../Modal';

interface TooltipProps {
  children: React.ReactNode;
  popover: React.ReactNode;
  backgroundColor?: string;
  height?: number;
  width?: number;
  autoSize?: boolean;
}

export const Tooltip: React.FC<TooltipProps> = ({
  children,
  popover,
  backgroundColor,
  height = 80,
  width = 220,
  autoSize = false,
}) => {
  const { styles } = useStyles(stylesheet);
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [tooltipSize, setTooltipSize] = useState({ width, height: height as number });
  const triggerRef = useRef<View>(null);

  const showTooltip = () => {
    if (triggerRef.current) {
      triggerRef.current.measure(
        (x: number, y: number, triggerWidth: number, triggerHeight: number, pageX: number, pageY: number) => {
          const screenWidth = Dimensions.get('window').width;
          const screenHeight = Dimensions.get('window').height;
          const padding = 16;

          const currentWidth = autoSize ? Math.min(screenWidth - padding * 2, 216) : width;
          const currentHeight = height;

          let tooltipX = pageX + triggerWidth / 2 - currentWidth / 2;
          let tooltipY = pageY;

          if (tooltipX < padding) {
            tooltipX = padding;
          } else if (tooltipX + currentWidth > screenWidth - padding) {
            tooltipX = screenWidth - currentWidth - padding;
          }

          if (tooltipY < padding) {
            tooltipY = pageY + triggerHeight + 28;
          }

          if (tooltipY + currentHeight > screenHeight - padding) {
            tooltipY = screenHeight - currentHeight - padding;
          }

          setTooltipSize({ width: currentWidth, height: currentHeight });
          setTooltipPosition({ x: tooltipX, y: tooltipY });
          setIsVisible(true);
        }
      );
    }
  };

  const hideTooltip = () => {
    setIsVisible(false);
  };

  return (
    <>
      <TouchableOpacity ref={triggerRef} onPress={showTooltip} activeOpacity={0.7}>
        {children}
      </TouchableOpacity>

      <ModalRn isVisible={isVisible} onBackdropPress={hideTooltip} style={styles.modal}>
        <View
          style={[
            styles.tooltipContainer,
            {
              backgroundColor: backgroundColor || '#333',
              height: autoSize ? undefined : tooltipSize.height,
              width: autoSize ? 'auto' : tooltipSize.width,
              maxWidth: autoSize ? tooltipSize.width : undefined,
              left: tooltipPosition.x,
              top: tooltipPosition.y,
            },
          ]}
        >
          {popover}
        </View>
      </ModalRn>
    </>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  modal: {
    margin: 0,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  tooltipContainer: {
    position: 'absolute',
    borderRadius: 12,
  },
}));
