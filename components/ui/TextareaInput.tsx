import { useState } from 'react';
import { TextInput, TextInputProps, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '../ThemedText';

interface TextareaInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: any;
  maxLength?: number;
}

export const TextareaInput = ({
  label,
  error,
  containerStyle,
  maxLength,
  editable,
  value,
  onFocus,
  onBlur,
  ...rest
}: TextareaInputProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const { styles, theme } = useStyles(stylesheet);

  const hasError = !!error;

  const handleFocus = (e: any) => {
    setIsFocused(true);
    if (onFocus) onFocus(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    if (onBlur) onBlur(e);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <ThemedText style={styles.label}>{label}</ThemedText>}

      <View
        style={[styles.inputContainer, isFocused ? styles.inputFocused : null, hasError ? styles.inputError : null]}
      >
        <TextInput
          style={[styles.input, editable === false ? styles.inputContainerDisabled : null]}
          multiline
          textAlignVertical='top'
          maxLength={maxLength}
          value={value}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor={theme.colors.whiteOpacity40}
          numberOfLines={20}
          {...rest}
        />
      </View>

      <View style={styles.footer}>{error && <ThemedText style={styles.errorText}>{error}</ThemedText>}</View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    width: '100%',
  },
  inputContainerDisabled: {
    opacity: 0.6,
  },
  label: {
    marginBottom: 8,
    color: theme.colors.neutralGrey,
  },
  inputContainer: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 222,
    borderWidth: 1,
    borderColor: theme.colors.neutralCard,
  },
  input: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    ...theme.fw400,
    flex: 1,
  },
  errorInput: {
    borderColor: theme.colors.stateError,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  errorText: {
    color: theme.colors.stateError,
    fontSize: 12,
    flex: 1,
    textAlign: 'right',
  },
  counter: {
    fontSize: 12,
    color: theme.colors.neutralGrey,
    textAlign: 'right',
  },
  inputFocused: {
    borderColor: theme.colors.primary,
  },
  inputError: {
    borderColor: theme.colors.stateError,
  },
}));
