import { Image, ImageProps } from 'expo-image';
import { useState } from 'react';
import Animated from 'react-native-reanimated';

type TSource = ImageProps['source'];
type Props = {} & ImageProps;

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

function getRecyclingKeyFromSource(source: TSource) {
  if (!source) return null;

  // If source is a string (URI)
  if (typeof source === 'string') {
    return source;
  }

  // If source is an object with uri key (but not an array)
  if (typeof source === 'object' && !Array.isArray(source) && source && 'uri' in source) {
    return source.uri;
  }

  // If source is an array, concatenate all URIs
  if (Array.isArray(source) && source.length > 0) {
    const uris = source
      .map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        if (typeof item === 'object' && item && 'uri' in item) {
          return item.uri;
        }
        return '';
      })
      .filter((uri) => uri !== '');

    return uris.join(',');
  }

  // If source is a local asset (number returned by require)
  // You can convert it to string or use JSON.stringify as fallback
  if (typeof source === 'number') {
    return source.toString();
  }

  // Fallback
  return JSON.stringify(source);
}

export const ExpoImage = (props: Props) => {
  const [source, setSource] = useState<TSource>(props.source);

  const handleLoadError = (error: any) => {
    console.error(error);
    setSource(require('@/assets/images/empty-cover.png'));
  };

  if (!props.source) {
    return null;
  }

  const recyclingKey = getRecyclingKeyFromSource(source);

  console.log(1, source);

  return (
    <Image
      transition={200}
      cachePolicy='disk'
      onError={handleLoadError}
      placeholder={{ blurhash }}
      recyclingKey={recyclingKey}
      {...props}
      source={source}
    />
  );
};

export const ExpoImageAnimated = Animated.createAnimatedComponent(ExpoImage);
