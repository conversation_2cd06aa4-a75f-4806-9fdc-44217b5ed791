import { Image, ImageProps } from 'expo-image';
import { useEffect, useState } from 'react';
import Animated from 'react-native-reanimated';

type TSource = ImageProps['source'];
type Props = {} & ImageProps;

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const ExpoImage = (props: Props) => {
  const [source, setSource] = useState<TSource>(props.source);

  const handleLoadError = (error: any) => {
    console.error(error);
    console.log('Source is', props.source);
    setSource(require('@/assets/images/empty-cover.png'));
  };

  const recyclingKey = props?.source?.uri ? props?.source?.uri : undefined;

  console.log({ recyclingKey });

  useEffect(() => {
    first
  
    return () => {
      second
    }
  }, [third])
  

  if (!source) {
    return null;
  }

  return (
    <Image
      // transition={200}
      // cachePolicy='disk'
      onError={handleLoadError}
      placeholder={{ blurhash }}
      // recyclingKey={recyclingKey}
      {...props}
      source={props.source}
    />
  );
};

export const ExpoImageAnimated = Animated.createAnimatedComponent(ExpoImage);
