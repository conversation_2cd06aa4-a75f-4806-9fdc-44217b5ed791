import { createSafeContext } from '@/utils/create-safe-context';
import { RefObject } from 'react';
import { ScrollViewProps } from 'react-native';
import PagerView from 'react-native-pager-view';
import { SharedValue } from 'react-native-reanimated';

type CollapsingTabsContextProps = {
  sharedProps: Partial<ScrollViewProps>;
  currentScroll: any;
  tabName: string;
  tabIndex: number;
  headerDiff: number;
  headerContainerStyle: any;
  tabBarStyle: any;
  handleHeaderLayout: any;
  isScrolling: SharedValue<number>;
  progressIndicator: SharedValue<number>;
  translateY: SharedValue<number>;
  refPager: RefObject<PagerView | null>;
  onIndexChange: (index: number) => void;
  onTabNameChange: (name: string) => void;
};

export const [CollapsingTabsContextProvider, useCollapsingTabsContext] = createSafeContext<CollapsingTabsContextProps>(
  'CollapsingTabsContextProvider component was not found in tree'
);
