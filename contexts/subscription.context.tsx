import { createSafeContext } from '@/utils/create-safe-context';
import { CustomerInfo, PurchasesPackage } from 'react-native-purchases';

type SubscriptionContextProps = {
  packages: PurchasesPackage[];
  isReady: boolean;
  isPurchasing: boolean;
  customerInfo: CustomerInfo | undefined;
  purchasePackage: (purchasePackage: PurchasesPackage) => void;
  restorePermissions: () => void;
  cancelPurchase: () => Promise<void>;
};

export const [SubscriptionContextProvider, useSubscriptionContext] = createSafeContext<SubscriptionContextProps>(
  'SubscriptionContextProvider component was not found in tree'
);
